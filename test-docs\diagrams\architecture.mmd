flowchart TB
  %% ========== 顶层参与方 ==========
  subgraph AI[AI/MCP 客户端]
    <PERSON>[Claude / 其它 MCP 客户端]
  end

  %% ========== 原生服务器层 ==========
  subgraph Server[原生服务器 (app/native-server)]
    Fastify[Fastify HTTP 服务器\n端点: /mcp (Streamable HTTP), /sse (SSE)\n端口: 127.0.0.1:12306]
    MCPServer[MCP SDK Server\ngetMcpServer()]
    Transports[传输层\nStreamableHTTPServerTransport\nSSEServerTransport]
    Registry[工具注册与分发\nregister-tools.ts]
  end

  %% ========== 原生消息主机 ==========
  subgraph NativeHost[原生消息主机 (native-messaging-host.ts)]
    Pending[请求路由与超时\npendingRequests Map (requestId/resolve/reject/timeout)]
  end

  %% ========== Chrome 扩展层 ==========
  subgraph Ext[Chrome 扩展 (app/chrome-extension)]
    BG[背景页 background\nindex.ts / native-host.ts]
    Tools[工具执行器\nbackground/tools/*]
    CS[内容脚本\ninject-scripts/*.js]
    Offscreen[离屏文档\noffscreen/main.ts\n语义引擎/向量搜索]
    Popup[弹窗 UI\npopup/App.vue]
  end

  %% ========== 浏览器能力与存储 ==========
  subgraph Browser[浏览器能力与存储]
    ChromeAPIs[Chrome APIs\nbookmarks/history/debugger/webRequest\n scripting/tabs/runtime]
    Storage[chrome.storage.local]
    IndexedDB[IndexedDB\n向量索引/模型缓存]
  end

  Shared[共享包 packages/shared\nNativeMessageType, TOOL_SCHEMAS]

  %% ========== 请求主路径 ==========
  Claude -->|HTTP POST /mcp\nGET/DELETE /mcp (SSE)\n协议: Streamable HTTP / SSE| Fastify
  Fastify --> MCPServer
  MCPServer --> Transports
  MCPServer --> Registry
  Registry -->|NativeMessageType.CALL_TOOL\nsendRequestToExtensionAndWait(requestId)| Pending
  Pending -->|Chrome Native Messaging\nstdin/stdout + 4字节长度头| BG

  %% 扩展内部分发
  BG -->|chrome.runtime.connectNative\npostMessage / responseToRequestId| Pending
  BG -->|按 name 分发| Tools
  Tools -->|chrome.scripting.executeScript\nchrome.tabs.sendMessage| CS
  Tools -->|直接调用| ChromeAPIs
  Tools -->|消息 (OFFSCREEN_MESSAGE_TYPES)| Offscreen
  Offscreen --> Storage
  Offscreen --> IndexedDB
  CS -->|页面交互/DOM| ChromeAPIs

  %% 结果回传
  Tools -->|CallToolResult| BG
  BG -->|responseToRequestId| Pending
  Pending -->|MCP 响应 (CallToolResult)| MCPServer
  MCPServer --> Claude

  %% 共享定义关系
  Shared --> Registry
  Shared --> BG

  %% ========== 错误与异常传播 ==========
  Pending -.->|超时/解析失败\nreject(Error)| MCPServer
  Fastify -.->|400/500\nINVALID_MCP_REQUEST 等| Claude
  BG -.->|TOOL_EXECUTION_FAILED| Pending
  Offscreen -.->|UPDATE_MODEL_STATUS 错误状态| BG

