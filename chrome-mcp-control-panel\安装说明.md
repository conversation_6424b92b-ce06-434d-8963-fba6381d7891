# Chrome MCP 控制面板安装说明

## 问题解决状态 ✅

**问题已修复！** MCP 连接错误已解决。

### 修复的问题：
1. **Accept 头缺失** - 添加了必需的 `text/event-stream` Accept 头
2. **MCP 服务器启动** - 创建了测试服务器启动脚本
3. **端口配置** - 保持原项目默认端口 `12306`

## 安装步骤

### 1. 启动 MCP 服务器
```bash
cd app/native-server
node test-server.js
```

服务器将在端口 12306 上运行。

### 2. 加载 Chrome 扩展

1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. **如果之前已加载过扩展**：
   - 找到 "Chrome MCP Control Panel" 扩展
   - 点击"重新加载"按钮 🔄
5. **如果是首次加载**：
   - 点击"加载已解压的扩展程序"
   - 选择 `chrome-mcp-control-panel/dist` 文件夹
6. 扩展将被加载并显示在扩展列表中

⚠️ **重要**：如果之前加载过旧版本，必须重新加载扩展才能应用端口修复！

### 3. 使用控制面板

1. 点击浏览器工具栏中的控制面板图标
2. 控制面板将自动连接到 MCP 服务器
3. 现在应该能看到可用的工具列表，不再有连接错误

## 验证连接

如果一切正常，你应该看到：
- ✅ 服务器状态显示为"已连接"
- ✅ 工具列表显示可用的 MCP 工具
- ❌ 不再有"Failed to fetch"或"406"错误

## 故障排除

如果仍有问题：

1. **检查服务器是否运行**：
   ```bash
   netstat -ano | findstr :12306
   ```

2. **检查控制台错误**：
   - 按 F12 打开开发者工具
   - 查看 Console 标签页的错误信息

3. **重新构建控制面板**：
   ```bash
   cd chrome-mcp-control-panel
   npm run build
   ```

## 技术细节

- **MCP 服务器端口**: 12306
- **控制面板端点**: http://127.0.0.1:12306/mcp
- **协议版本**: MCP 2024-11-05
- **传输方式**: HTTP + Server-Sent Events (SSE)
