<template>
  <div class="popup-container">
    <!-- 头部状态栏 -->
    <div class="header">
      <div class="status-indicator">
        <div :class="['status-dot', connectionStatus]"></div>
        <span class="status-text">{{ statusText }}</span>
      </div>
      <button @click="openOptions" class="options-button">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      </button>
    </div>

    <!-- 工具统计 -->
    <div v-if="isConnected" class="tools-summary">
      <div class="summary-card">
        <div class="summary-number">{{ toolsCount }}</div>
        <div class="summary-label">可用工具</div>
      </div>
      <div class="summary-card">
        <div class="summary-number">{{ favoriteTools.length }}</div>
        <div class="summary-label">收藏工具</div>
      </div>
      <div class="summary-card">
        <div class="summary-number">{{ recentTools.length }}</div>
        <div class="summary-label">最近使用</div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div v-if="isConnected" class="quick-actions">
      <h3 class="section-title">快速操作</h3>
      <div class="action-grid">
        <button @click="executeQuickAction('chrome_screenshot', { fullPage: true })" class="action-button">
          <svg class="action-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          <span>截屏</span>
        </button>

        <button @click="executeQuickAction('chrome_highlight_page_elements', { enable: true })" class="action-button">
          <svg class="action-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
          </svg>
          <span>高亮元素</span>
        </button>

        <button @click="executeQuickAction('get_windows_and_tabs', {})" class="action-button">
          <svg class="action-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H5a2 2 0 00-2 2v12a2 2 0 002 2h14a2 2 0 002-2V6a2 2 0 00-2-2z" />
          </svg>
          <span>窗口信息</span>
        </button>

        <button @click="executeQuickAction('chrome_get_web_content', {})" class="action-button">
          <svg class="action-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <span>页面内容</span>
        </button>
      </div>
    </div>

    <!-- 最近使用工具 -->
    <div v-if="isConnected && recentTools.length > 0" class="recent-tools">
      <h3 class="section-title">最近使用</h3>
      <div class="tool-list">
        <div
          v-for="toolName in recentTools.slice(0, 3)"
          :key="toolName"
          @click="openToolInOptions(toolName)"
          class="tool-item"
        >
          <div class="tool-name">{{ formatToolName(toolName) }}</div>
          <svg class="tool-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </div>
      </div>
    </div>

    <!-- 连接失败提示 + 诊断 -->
    <div v-if="!isConnected" class="connection-error">
      <div class="error-icon">⚠️</div>
      <h3>无法连接到MCP服务器</h3>
      <p>请确保chrome-mcp扩展正在运行</p>
      <div class="diagnostics">
        <div class="diag-actions">
          <button @click="runDiagnostics" class="reconnect-button">运行连接诊断</button>
          <button @click="runBridgeSelfCheck" class="reconnect-button" style="margin-left:8px;">外部桥接自检</button>
        </div>
        <div v-if="diag.running" class="diag-log">正在诊断...</div>
        <div v-if="diag.logs.length" class="diag-log">
          <div v-for="(line, i) in diag.logs" :key="i">{{ line }}</div>
        </div>
      </div>
      <button @click="reconnect" class="reconnect-button">重新连接</button>
    </div>

    <!-- 底部操作 -->
    <div class="footer">
      <button @click="openOptions" class="footer-button primary">
        打开完整控制面板
      </button>
      <button @click="refreshStatus" class="footer-button secondary">
        刷新状态
      </button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'

// 响应式数据
const isConnected = ref(false)
const toolsCount = ref(0)
const favoriteTools = ref<string[]>([])
const recentTools = ref<string[]>([])
const serverInfo = ref<any>(null)

// 计算属性
const connectionStatus = computed(() => isConnected.value ? 'connected' : 'disconnected')
const statusText = computed(() => isConnected.value ? `已连接 - ${toolsCount.value}个工具` : '未连接')

// 诊断状态
const diag = ref<{ running: boolean; logs: string[] }>({ running: false, logs: [] })

// 初始化
onMounted(async () => {
  await loadStatus()
  await loadFavoriteTools()
  await loadRecentTools()
})

// 加载状态
async function loadStatus() {
  try {
    const response = await chrome.runtime.sendMessage({ action: 'getMCPStatus' })
    if (response.success) {
      isConnected.value = response.data.connected
      toolsCount.value = response.data.toolsCount
      serverInfo.value = response.data.serverInfo
    }
  } catch (error) {
    console.error('加载状态失败:', error)
  }
}

// 运行诊断：扩展桥接、直连端点探测、SSE 可用性
async function runDiagnostics() {
  diag.value.running = true
  diag.value.logs = []
  const log = (m: string) => diag.value.logs.push(m)

  try {
    log('开始诊断...')
    // 1) 扩展桥接：后台统计
    try {
      const ping = await chrome.runtime.sendMessage({ action: 'getMCPStatus' })
      log(`后台 getMCPStatus 返回：connected=${ping?.data?.connected}, tools=${ping?.data?.toolsCount}`)
    } catch (e) {
      log('后台通信失败（getMCPStatus）')
    }
    // 2) 直连端点：依赖后台的回退策略（已实现）
    try {
      await chrome.runtime.sendMessage({ action: 'reconnect' })
      const st = await chrome.runtime.sendMessage({ action: 'getMCPStatus' })
      log(`直连/扩展重试后状态：connected=${st?.data?.connected}, tools=${st?.data?.toolsCount}`)
    } catch (e) {
      log('重连失败')
    }
  } catch (e) {
    log(`诊断异常：${e instanceof Error ? e.message : String(e)}`)
  } finally {
    diag.value.running = false
  }
}

// 外部桥接自检：读取存储ID → ping → connectNative → 查看统计
async function runBridgeSelfCheck() {
  diag.value.running = true
  diag.value.logs = []
  const log = (m: string) => diag.value.logs.push(m)
  try {
    const { chromeMcpExtensionId } = await chrome.storage.local.get(['chromeMcpExtensionId'])
    if (!chromeMcpExtensionId) {
      log('未在存储中找到主扩展ID（chromeMcpExtensionId）')
      return
    }
    log('使用主扩展ID：' + chromeMcpExtensionId)
    // ping
    try {
      const r1 = await new Promise<any>((resolve, reject) => {
        chrome.runtime.sendMessage(chromeMcpExtensionId, { action: 'ping', source: 'popup-bridge-selfcheck' }, (resp) => {
          if (chrome.runtime.lastError) reject(new Error(chrome.runtime.lastError.message)); else resolve(resp)
        })
      })
      log('ping 响应：' + JSON.stringify(r1))
    } catch (e) {
      log('ping 失败：' + (e instanceof Error ? e.message : String(e)))
    }
    // connectNative
    try {
      const r2 = await new Promise<any>((resolve, reject) => {
        chrome.runtime.sendMessage(chromeMcpExtensionId, { action: 'connectNative' }, (resp) => {
          if (chrome.runtime.lastError) reject(new Error(chrome.runtime.lastError.message)); else resolve(resp)
        })
      })
      log('connectNative 响应：' + JSON.stringify(r2))
    } catch (e) {
      log('connectNative 失败：' + (e instanceof Error ? e.message : String(e)))
    }
    // 再取后台状态
    const st = await chrome.runtime.sendMessage({ action: 'getMCPStatus' })
    log(`当前状态：connected=${st?.data?.connected}, tools=${st?.data?.toolsCount}`)
  } catch (e) {
    log('外部桥接自检异常：' + (e instanceof Error ? e.message : String(e)))
  } finally {
    diag.value.running = false
  }
}

// 加载收藏工具
async function loadFavoriteTools() {
  try {
    const response = await chrome.runtime.sendMessage({ action: 'getFavoriteTools' })
    if (response.success) {
      favoriteTools.value = response.data
    }
  } catch (error) {
    console.error('加载收藏工具失败:', error)
  }
}

// 加载最近使用工具
async function loadRecentTools() {
  try {
    const response = await chrome.runtime.sendMessage({ action: 'getRecentTools' })
    if (response.success) {
      recentTools.value = response.data
    }
  } catch (error) {
    console.error('加载最近使用工具失败:', error)
  }
}

// 执行快速操作
async function executeQuickAction(toolName: string, parameters: any) {
  try {
    const response = await chrome.runtime.sendMessage({
      action: 'callTool',
      toolName,
      parameters
    })

    if (response.success) {
      console.log('快速操作执行成功:', response.data)
      // 刷新最近使用工具列表
      await loadRecentTools()
    } else {
      console.error('快速操作执行失败:', response.error)
    }
  } catch (error) {
    console.error('快速操作执行错误:', error)
  }
}

// 打开选项页面
function openOptions() {
  chrome.runtime.openOptionsPage()
  window.close()
}

// 在选项页面中打开指定工具
function openToolInOptions(toolName: string) {
  chrome.runtime.openOptionsPage()
  // 通过URL参数传递工具名称
  chrome.tabs.create({
    url: chrome.runtime.getURL(`src/pages/options.html#/tool/${toolName}`)
  })
  window.close()
}

// 重新连接
async function reconnect() {
  try {
    await chrome.runtime.sendMessage({ action: 'reconnect' })
    await loadStatus()
  } catch (error) {
    console.error('重新连接失败:', error)
  }
}

// 刷新状态
async function refreshStatus() {
  await loadStatus()
  await loadFavoriteTools()
  await loadRecentTools()
}

// 格式化工具名称
function formatToolName(toolName: string): string {
  return toolName.replace(/^chrome_/, '').replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}
</script>
