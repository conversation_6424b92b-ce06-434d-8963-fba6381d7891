<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg32" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea"/>
      <stop offset="100%" style="stop-color:#764ba2"/>
    </linearGradient>
  </defs>
  <rect width="32" height="32" rx="6" fill="url(#bg32)"/>
  <circle cx="12" cy="11" r="2" fill="white"/>
  <circle cx="20" cy="11" r="2" fill="white"/>
  <circle cx="16" cy="19" r="2" fill="white"/>
  <line x1="12" y1="11" x2="20" y2="11" stroke="white" stroke-width="1" stroke-linecap="round"/>
  <line x1="16" y1="14" x2="16" y2="17" stroke="white" stroke-width="1" stroke-linecap="round"/>
</svg>