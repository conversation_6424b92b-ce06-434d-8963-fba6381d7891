# 异常与超时时序

推荐查看：VS Code / Obsidian / GitHub Web（Markdown + Mermaid）。

## 场景一：扩展执行超时
```mermaid
sequenceDiagram
  participant Client as MCP 客户端
  participant Fastify as 原生服务器/Fastify
  participant MCP as MCP Server
  participant Reg as 工具注册路由
  participant Host as 原生消息主机
  participant BG as 扩展背景页

  Client->>Fastify: POST /mcp (callTool)
  Fastify->>MCP: transport.handleRequest
  MCP->>Reg: CallTool handler
  Reg->>Host: sendRequestToExtensionAndWait(timeout=30s)
  Host->>BG: postMessage({requestId})
  Note over BG: 工具执行缓慢/无响应
  Host-->>Reg: <timeout> reject(Error("Request timed out"))
  Reg-->>Client: CallToolResult { isError: true, content:["Error calling tool: Request timed out..."] }
```

## 场景二：Native Host 断连
```mermaid
sequenceDiagram
  participant Client as MCP 客户端
  participant Fastify as 原生服务器/Fastify
  participant MCP as MCP Server
  participant Reg as 工具注册路由
  participant Host as 原生消息主机
  participant BG as 扩展背景页

  Client->>Fastify: POST /mcp (callTool)
  Reg->>Host: sendRequestToExtensionAndWait
  Note over Host: stdin end / error
  Host->>Host: cleanup() 取消所有 pendingRequests
  Host-->>Reg: reject(Error('Native host is shutting down...'))
  Reg-->>Client: CallToolResult { isError: true, content:["Error calling tool: Native host is shutting down..."] }
```

## 场景三：MCP 会话/请求无效
```mermaid
sequenceDiagram
  participant Client as MCP 客户端
  participant Fastify as 原生服务器/Fastify

  Client->>Fastify: POST /mcp (非 initialize 且无 sessionId)
  Fastify-->>Client: 400 { error: INVALID_MCP_REQUEST }
```

## 错误源与定位
- 扩展工具层：background/tools/* 捕获 → createErrorResponse → 统一 isError=true
- 原生消息：native-messaging-host.ts 超时/断连 → reject(Error)
- HTTP/MCP：server/index.ts 捕获 → 400/500 / 错误消息

## 查看建议
- VS Code / Obsidian 推荐；GitHub Web 可在线预览。
