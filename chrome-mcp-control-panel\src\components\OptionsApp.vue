<template>
  <div class="options-container">
    <!-- 顶部导航栏 -->
    <header class="options-header">
      <div class="header-content">
        <div class="logo-section">
          <div class="logo-icon">🎛️</div>
          <div>
            <h1 class="app-title">Chrome MCP Control Panel</h1>
            <p class="app-subtitle">专业的浏览器自动化工具控制面板</p>
          </div>
        </div>
        <div class="connection-status">
          <div :class="['status-indicator', connectionStatus]">
            <div class="status-dot"></div>
            <span>{{ statusText }}</span>
          </div>
        </div>
      </div>
    </header>

    <!-- 主内容区 -->
    <main class="main-content">
      <!-- 侧边栏 -->
      <nav class="sidebar">
        <div class="nav-section">
          <h3 class="nav-title">工具分类</h3>
          <ul class="nav-menu">
            <li
              v-for="(category, key) in toolCategories"
              :key="key"
              :class="['nav-item', { active: activeCategory === key }]"
              @click="setActiveCategory(key)"
            >
              <span class="nav-icon">{{ category.icon }}</span>
              <span class="nav-label">{{ category.name }}</span>
              <span class="nav-count">{{ category.tools.length }}</span>
            </li>
          </ul>
        </div>

        <div class="nav-section">
          <h3 class="nav-title">快速访问</h3>
          <ul class="nav-menu">
            <li class="nav-item" @click="showWebController">
              <span class="nav-icon">🌐</span>
              <span class="nav-label">网页控制</span>
            </li>
            <li class="nav-item" @click="showFavorites">
              <span class="nav-icon">⭐</span>
              <span class="nav-label">收藏工具</span>
              <span class="nav-count">{{ favoriteTools.length }}</span>
            </li>
            <li class="nav-item" @click="showRecent">
              <span class="nav-icon">🕐</span>
              <span class="nav-label">最近使用</span>
              <span class="nav-count">{{ recentTools.length }}</span>
            </li>
            <li class="nav-item" @click="showSettings">
              <span class="nav-icon">⚙️</span>
              <span class="nav-label">设置</span>
            </li>
            <li class="nav-item" @click="showBridgeSettings">
              <span class="nav-icon">🧩</span>
              <span class="nav-label">桥接配置</span>
            </li>
          </ul>
        </div>
      </nav>

      <!-- 工具内容区 -->
      <section class="content-area">
        <!-- 桥接配置视图 -->
        <div v-if="currentView === 'bridge-settings'" class="bridge-settings">
          <h2>桥接配置</h2>
          <p class="mb-2">配置主扩展（chrome-mcp-server）的真实ID，用于扩展桥接通信。</p>
          <div class="form-field">
            <label for="extId" class="field-label">主扩展ID（32位）</label>
            <input id="extId" v-model="bridge.extId" class="form-input" placeholder="例如：blffhgeabaflafmmagoomiokiedgnbge" />
            <p class="field-hint">在 chrome://extensions/ 开启开发者模式后，从“Chrome MCP Server”扩展卡片复制“ID”。</p>
          </div>
          <div class="form-actions">
            <button class="execute-btn" @click="saveBridgeExtId">保存ID</button>
            <button class="reset-btn" @click="pingBridge">测试连接（ping）</button>
            <button class="reset-btn" @click="connectNative">拉起原生主机（connectNative）</button>
          </div>
          <div v-if="bridge.log" class="result-panel"><pre>{{ bridge.log }}</pre></div>
        </div>

        <!-- 网页控制器视图 -->
        <WebPageController v-if="currentView === 'web-controller'" />

        <!-- 工具列表视图 -->
        <div v-if="currentView === 'tools'" class="tools-grid">
          <div
            v-for="tool in filteredTools"
            :key="tool.name"
            class="tool-card"
            @click="selectTool(tool)"
          >
            <div class="tool-header">
              <h3 class="tool-name">{{ formatToolName(tool.name) }}</h3>
              <button
                class="favorite-btn"
                :class="{ active: favoriteTools.includes(tool.name) }"
                @click.stop="toggleFavorite(tool.name)"
              >
                ⭐
              </button>
            </div>
            <p class="tool-description">{{ tool.description }}</p>
            <div class="tool-footer">
              <span class="tool-params">{{ Object.keys(tool.inputSchema.properties || {}).length }} 参数</span>
            </div>
          </div>
        </div>

        <!-- 工具详情和执行界面 -->
        <div v-if="currentView === 'tool-detail' && selectedTool" class="tool-detail">
          <div class="detail-header">
            <button class="back-btn" @click="goBack">← 返回</button>
            <h2 class="detail-title">{{ formatToolName(selectedTool.name) }}</h2>
            <button
              class="favorite-btn"
              :class="{ active: favoriteTools.includes(selectedTool.name) }"
              @click="toggleFavorite(selectedTool.name)"
            >
              ⭐
            </button>
          </div>

          <div class="detail-content">
            <div class="tool-info">
              <p class="tool-desc">{{ selectedTool.description }}</p>
            </div>

            <!-- 动态生成的参数表单 -->
            <div class="parameter-form">
              <h3>参数配置</h3>
              <form @submit.prevent="executeTool">
                <div
                  v-for="field in toolFormConfig.fields"
                  :key="field.key"
                  class="form-field"
                >
                  <label :for="field.key" class="field-label">
                    {{ field.label }}
                    <span v-if="field.required" class="required">*</span>
                  </label>

                  <!-- 文本输入 -->
                  <input
                    v-if="field.type === 'text' || field.type === 'url'"
                    v-model="formData[field.key]"
                    :type="field.type"
                    :id="field.key"
                    :placeholder="field.placeholder"
                    :required="field.required"
                    class="form-input"
                  />

                  <!-- 数字输入 -->
                  <input
                    v-else-if="field.type === 'number'"
                    v-model.number="formData[field.key]"
                    type="number"
                    :id="field.key"
                    :min="field.min"
                    :max="field.max"
                    :placeholder="field.placeholder"
                    :required="field.required"
                    class="form-input"
                  />

                  <!-- 开关 -->
                  <label
                    v-else-if="field.type === 'switch'"
                    :for="field.key"
                    class="switch-container"
                  >
                    <input
                      v-model="formData[field.key]"
                      type="checkbox"
                      :id="field.key"
                      class="switch-input"
                    />
                    <span class="switch-slider"></span>
                  </label>

                  <!-- 选择框 -->
                  <select
                    v-else-if="field.type === 'select'"
                    v-model="formData[field.key]"
                    :id="field.key"
                    :required="field.required"
                    class="form-select"
                  >
                    <option value="">请选择...</option>
                    <option
                      v-for="option in field.options"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </option>
                  </select>

                  <p v-if="field.hint" class="field-hint">{{ field.hint }}</p>
                </div>

                <div class="form-actions">
                  <button
                    type="submit"
                    class="execute-btn"
                    :disabled="isExecuting"
                  >
                    {{ isExecuting ? '执行中...' : '执行工具' }}
                  </button>
                  <button
                    type="button"
                    class="reset-btn"
                    @click="resetForm"
                  >
                    重置
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- 执行结果显示 -->
        <div v-if="lastResult" class="result-panel">
          <h3 class="result-title">执行结果</h3>
          <div :class="['result-content', lastResult.success ? 'success' : 'error']">
            <pre>{{ JSON.stringify(lastResult, null, 2) }}</pre>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue'
import type { MCPTool } from '@/types/mcp'
import UIGeneratorService from '@/services/uiGenerator'
import ChromeExtensionBridge from '@/services/chromeExtensionBridge'
import WebPageController from './WebPageController.vue'

// 响应式数据
const isConnected = ref(false)
const toolsCount = ref(0)
const allTools = ref<MCPTool[]>([])
const toolCategories = reactive<Record<string, any>>({})
const favoriteTools = ref<string[]>([])
const recentTools = ref<string[]>([])
const serverInfo = ref<any>(null)

// UI状态
const currentView = ref('tools')
const activeCategory = ref('all')
const selectedTool = ref<MCPTool | null>(null)
const formData = reactive<Record<string, any>>({})
const isExecuting = ref(false)
const lastResult = ref<any>(null)

// 桥接配置
const bridge = reactive<{ extId: string; log: string }>({ extId: '', log: '' })

// UI生成器
const uiGenerator = UIGeneratorService.getInstance()
const toolFormConfig = ref<any>({})

// 计算属性
const connectionStatus = computed(() => isConnected.value ? 'connected' : 'disconnected')
const statusText = computed(() =>
  isConnected.value
    ? `已连接 - ${toolsCount.value} 个工具可用`
    : '未连接到MCP服务器'
)

const filteredTools = computed(() => {
  if (activeCategory.value === 'all') {
    return allTools.value
  }
  return toolCategories[activeCategory.value]?.tools || []
})

// 初始化
onMounted(async () => {
  // 读取已保存的主扩展ID
  const stored = await chrome.storage.local.get(['chromeMcpExtensionId'])
  bridge.extId = stored.chromeMcpExtensionId || ''

  await loadMCPStatus()
  await loadTools()
  await loadFavoriteTools()
  await loadRecentTools()
})

// 加载MCP状态
async function loadMCPStatus() {
  try {
    const extensionBridge = ChromeExtensionBridge.getInstance()
    try {
      const server = await extensionBridge.detectAndConnect()
      isConnected.value = extensionBridge.isConnected()
      toolsCount.value = extensionBridge.getToolsCount()
      serverInfo.value = server
      console.log('✅ 通过chrome-mcp-server插件连接成功')
      return
    } catch (extensionError) {
      console.log('⚠️ chrome-mcp-server插件连接失败，尝试直连模式...')
    }
    const response = await chrome.runtime.sendMessage({ action: 'getMCPStatus' })
    if (response.success) {
      isConnected.value = response.data.connected
      toolsCount.value = response.data.toolsCount
      serverInfo.value = response.data.serverInfo
    }
  } catch (error) {
    console.error('加载MCP状态失败:', error)
    isConnected.value = false
    toolsCount.value = 0
  }
}

// 加载工具列表
async function loadTools() {
  try {
    const extensionBridge = ChromeExtensionBridge.getInstance()
    if (extensionBridge.isConnected()) {
      const tools = extensionBridge.getAllTools()
      const categorizedTools = extensionBridge.getToolsByCategory()
      allTools.value = tools
      updateToolCategories(categorizedTools)
      console.log(`✅ 从chrome-mcp-server插件获取到 ${tools.length} 个工具`)
      return
    }
    const response = await chrome.runtime.sendMessage({ action: 'getTools' })
    if (response.success) {
      allTools.value = response.data.tools
      updateToolCategories(response.data.categorizedTools)
    }
  } catch (error) {
    console.error('加载工具列表失败:', error)
    allTools.value = []
  }
}

// 更新工具分类
function updateToolCategories(categorized: Record<string, MCPTool[]>) {
  Object.keys(toolCategories).forEach(key => { delete toolCategories[key] })
  toolCategories.all = { name: '所有工具', icon: '📋', tools: allTools.value }
  Object.entries(categorized).forEach(([key, tools]) => {
    toolCategories[key] = { name: key, icon: getCategoryIcon(key), tools }
  })
}

function getCategoryIcon(category: string): string {
  const iconMap: Record<string, string> = {
    '浏览器控制': '🌐','信息获取': '📊','搜索功能': '🔍','网络请求': '🌍','书签管理': '🔖','历史记录': '📜','调试工具': '🔧','其他工具': '⚙️'
  }
  return iconMap[category] || '📁'
}

// 收藏 / 最近
async function loadFavoriteTools() { try { const r = await chrome.runtime.sendMessage({ action: 'getFavoriteTools' }); if (r.success) favoriteTools.value = r.data } catch (e) { console.error('加载收藏工具失败:', e) } }
async function loadRecentTools() { try { const r = await chrome.runtime.sendMessage({ action: 'getRecentTools' }); if (r.success) recentTools.value = r.data } catch (e) { console.error('加载最近使用工具失败:', e) } }

// 视图切换
function setActiveCategory(category: string) { activeCategory.value = category; currentView.value = 'tools' }
function selectTool(tool: MCPTool) { selectedTool.value = tool; currentView.value = 'tool-detail'; toolFormConfig.value = uiGenerator.generateFormConfig(tool); Object.keys(formData).forEach(k => delete formData[k]); toolFormConfig.value.fields.forEach((f: any) => { formData[f.key] = f.defaultValue || getDefaultValueByType(f.type) }) }
function goBack() { currentView.value = 'tools'; selectedTool.value = null }
function showWebController() { currentView.value = 'web-controller'; activeCategory.value = '' }
function showFavorites() { console.log('显示收藏工具') }
function showRecent() { console.log('显示最近使用') }
function showSettings() { console.log('显示设置') }
function showBridgeSettings() { currentView.value = 'bridge-settings' }

// 参数表单
function getDefaultValueByType(type: string): any { const m: Record<string, any> = { text: '', number: 0, switch: false, select: '', tags: [] }; return m[type] || '' }
async function executeTool() { if (!selectedTool.value) return; isExecuting.value = true; lastResult.value = null; try { const resp = await chrome.runtime.sendMessage({ action: 'callTool', toolName: selectedTool.value.name, parameters: { ...formData } }); lastResult.value = resp.data; await loadRecentTools() } catch (e) { lastResult.value = { success: false, error: e instanceof Error ? e.message : '执行失败' } } finally { isExecuting.value = false } }
function resetForm() { if (!toolFormConfig.value.fields) return; toolFormConfig.value.fields.forEach((f: any) => { formData[f.key] = f.defaultValue || getDefaultValueByType(f.type) }) }
async function toggleFavorite(toolName: string) { try { const isFav = favoriteTools.value.includes(toolName); const action = isFav ? 'removeFavoriteTool' : 'addFavoriteTool'; await chrome.runtime.sendMessage({ action, toolName }); await loadFavoriteTools() } catch (e) { console.error('切换收藏失败:', e) } }
function formatToolName(toolName: string): string { return toolName.replace(/^chrome_/, '').replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) }

// 桥接配置：保存/测试/拉起
async function saveBridgeExtId() {
  try {
    const id = bridge.extId.trim()
    if (!/^[a-p]{32}$/.test(id) && !/^[a-z]{32}$/.test(id)) {
      bridge.log = '扩展ID格式不符合预期（通常为32位小写字母）'
      return
    }
    await chrome.storage.local.set({ chromeMcpExtensionId: id })
    bridge.log = '已保存主扩展ID：' + id
  } catch (e) {
    bridge.log = '保存失败：' + (e instanceof Error ? e.message : String(e))
  }
}

async function pingBridge() {
  bridge.log = '正在 ping...'
  try {
    const id = bridge.extId.trim()
    const resp = await new Promise<any>((resolve, reject) => {
      chrome.runtime.sendMessage(id, { action: 'ping', source: 'options-bridge' }, (r) => {
        if (chrome.runtime.lastError) reject(new Error(chrome.runtime.lastError.message))
        else resolve(r)
      })
    })
    bridge.log = 'ping 响应：' + JSON.stringify(resp)
  } catch (e) {
    bridge.log = 'ping 失败：' + (e instanceof Error ? e.message : String(e))
  }
}

async function connectNative() {
  bridge.log = '正在请求主扩展连接原生主机...'
  try {
    const id = bridge.extId.trim()
    const resp = await new Promise<any>((resolve, reject) => {
      chrome.runtime.sendMessage(id, { action: 'connectNative' }, (r) => {
        if (chrome.runtime.lastError) reject(new Error(chrome.runtime.lastError.message))
        else resolve(r)
      })
    })
    bridge.log = 'connectNative 响应：' + JSON.stringify(resp)
  } catch (e) {
    bridge.log = 'connectNative 失败：' + (e instanceof Error ? e.message : String(e))
  }
}
</script>

<style scoped>
/* 这里将会有完整的CSS样式，由于篇幅限制，先使用基础样式 */
.options-container {
  min-height: 100vh;
  background: #f8fafc;
}

.options-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-icon {
  font-size: 32px;
}

.app-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
}

.app-subtitle {
  font-size: 14px;
  opacity: 0.8;
  margin: 4px 0 0 0;
}

.main-content {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  gap: 24px;
  padding: 24px;
}

.sidebar {
  width: 250px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  height: fit-content;
}

.content-area {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 24px;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.tool-card {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s;
}

.tool-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
}

.form-field {
  margin-bottom: 20px;
}

.form-input, .form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.execute-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
}

.result-panel {
  margin-top: 24px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
}

.result-content.success {
  color: #059669;
}

.result-content.error {
  color: #dc2626;
}
</style>
