# Chrome MCP 成功部署（第一阶段）经验总结

本文档记录从 G:\z-mcp-chrome 迁移至 G:\BaiduSyncdisk\chrome-mcp 后，确保扩展 ID 与跨扩展通信稳定的实施过程、关键配置与验证步骤，供后续长期维护参考。

## 一、背景与目标
- 迁移后避免“扩展 ID 变化导致连接失败”的问题反复发生
- 主扩展（chrome-mcp-server）与控制面板扩展均使用固定公钥（manifest.key），使扩展 ID 永久稳定
- 互相引用对端扩展 ID 的配置改为固定值，稳定跨扩展通信通道

## 二、固定公钥与稳定 ID 实施
1) 控制面板扩展注入固定 key（稳定其 ID）
- 源清单：chrome-mcp-control-panel/public/manifest.json
- 已添加字段：
  - key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmJ5BDGW+PVO7soqUJM528PdsO/DTkS0U6gf3va2MkuDJKBk8/VdndRLWfWxibpsNBz7l70MmnEvNbzLXaanYK5zrdH65/ipE1JYKIPOAvIUeZ7qOkbje9fuhiSuZDT2P7D077Bnt4hWW4CTPR0+v6cu/Z2v5XBDpclH5QuxFIorTiDHJlYhUKcXBLUeME0EB+5cbmxQ8qmNWZTqvjXX2mz8Mfizt9AC342+Z02b4LxfR1ot7E8t35CVdw+crttuAsgoEJMIjts3MEbhcwt+J9zse7TFuPhYaQR7S1LY7Cp8O+9/g+7WNpKCdI86M7AZDpAo0yYiuAEj7AY2oWB3AHwIDAQAB
- 由该 key 派生的固定控制面板扩展 ID：dnogdionebpilmfgafofocobiebdhkbl

2) 主扩展使用固定 key（稳定其 ID）
- 文件：app/chrome-extension/.env（字段 CHROME_EXTENSION_KEY 已存在且固定）
- 由该 key 派生的固定主扩展 ID：hbdgbgagpkpjffpklnamcljpakneikee

## 三、跨扩展通信白名单与探测逻辑
1) 主扩展的 externally_connectable 白名单
- 文件：app/chrome-extension/.env.local
- 设置：CONTROL_PANEL_EXTENSION_ID=dnogdionebpilmfgafofocobiebdhkbl
- 构建后产物：app/chrome-extension/.output/chrome-mv3/manifest.json 中应包含
  "externally_connectable": { "ids": ["dnogdionebpilmfgafofocobiebdhkbl"] }

2) 控制面板的主扩展探测逻辑
- 文件：chrome-mcp-control-panel/src/services/chromeExtensionBridge.ts
- 固定主扩展 ID：
  private readonly KNOWN_EXTENSION_IDS = [
    'hbdgbgagpkpjffpklnamcljpakneikee'
  ]
- 优先使用固定 ID 进行连接，失败时再走 storage/内容脚本兜底（不依赖 chrome:// 页面）

## 四、构建与产物位置
- 主扩展构建：
  cd app/chrome-extension
  pnpm run build
  产物：app/chrome-extension/.output/chrome-mv3
- 控制面板构建：
  cd chrome-mcp-control-panel
  pnpm run build
  产物：chrome-mcp-control-panel/dist

## 五、Chrome 重新加载步骤（标准化）
1) 打开 chrome://extensions/，启用“开发者模式”
2) 移除所有旧实例
3) 加载控制面板扩展：选择 G:\BaiduSyncdisk\chrome-mcp\chrome-mcp-control-panel\dist
4) 加载主扩展：选择 G:\BaiduSyncdisk\chrome-mcp\app\chrome-extension\.output\chrome-mv3
5) 在“详情”页确认扩展 ID 分别为：
   - 控制面板：dnogdionebpilmfgafofocobiebdhkbl
   - 主扩展：hbdgbgagpkpjffpklnamcljpakneikee

## 六、验证要点
- 控制面板诊断：connected=true，tools>0
- 主扩展与控制面板的 Background Console 无红色报错
- 主扩展 manifest.json 中 externally_connectable.ids 含控制面板固定 ID

## 七、常见问题与处理
- 错误加载“嵌套/重复目录”导致 ID 变化：确保仅从上述“正确路径”加载
- chrome:// 页面无法注入内容脚本：进行内容脚本探测时，请在普通 http/https 页面进行
- 直连 12306 返回非 JSON：如遇 SSE/非JSON 响应，优先使用扩展模式连接

## 八、清理与稳定性保障
- 已在仓库中去除混淆性目录，仅保留标准目录与构建产物
- 两端 key 与 ID 已固定，后续构建与发布均不会改变扩展 ID
- 若未来新增扩展通信方，统一纳入 .env.local / manifest 的固定 ID 清单

## 九、后续工作
- 进入“控制面板 UI 调整”阶段（独立提案与变更集）
- 若需自动化测试，可补充：
  - 构建后校验 manifest 中 key/ids 的脚本
  - 扩展间消息通道的端到端自检脚本

（完）

