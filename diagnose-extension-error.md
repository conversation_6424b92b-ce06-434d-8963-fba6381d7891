# 诊断扩展错误

## 从截图观察到的情况
- ✅ Chrome MCP 扩展已成功加载
- ✅ 扩展状态为"已启用"
- ❌ 扩展显示有错误信息

## 下一步诊断

### 第一步：查看详细错误信息
1. 在扩展卡片上点击 "错误" 按钮
2. 查看具体的错误详情
3. 记录错误信息的内容

### 第二步：检查后台脚本
1. 点击 "检查视图" 下的 "Service Worker" 或 "背景页"
2. 在打开的开发者工具中查看 Console 错误
3. 查看 Network 标签页是否有失败的请求

### 第三步：测试扩展功能
即使有错误，扩展可能仍然部分工作：

1. **点击扩展图标**：
   - 在浏览器工具栏中找到扩展图标
   - 点击查看是否有弹出窗口

2. **检查右键菜单**：
   - 在 httpbin.org 页面上右键点击
   - 查看是否有 MCP 相关的菜单项

3. **测试页面注入功能**：
   - 在 httpbin.org 页面按 F12 打开开发者工具
   - 在 Console 中执行：
   ```javascript
   // 检查是否有 MCP 相关的全局对象
   console.log('MCP objects:', Object.keys(window).filter(key => key.toLowerCase().includes('mcp')));
   
   // 检查是否有高亮相关的函数
   console.log('Highlight functions:', Object.keys(window).filter(key => key.toLowerCase().includes('highlight')));
   ```

### 第四步：手动触发高亮功能
如果扩展部分工作，尝试手动触发高亮：

```javascript
// 在页面 Console 中执行
// 方法1：查找可能的高亮函数
if (typeof highlightPageElements === 'function') {
  highlightPageElements();
}

// 方法2：查找 MCP 相关对象
if (window.mcpBridge && typeof window.mcpBridge.highlightElements === 'function') {
  window.mcpBridge.highlightElements();
}

// 方法3：发送消息给扩展
if (window.chrome && window.chrome.runtime) {
  chrome.runtime.sendMessage({action: 'highlight_page_elements'});
}
```

### 第五步：检查扩展权限
确认扩展具有必要的权限：
1. 在扩展详情页面查看权限列表
2. 确认包含：
   - "访问您在所有网站上的数据"
   - "与本机应用通信"
   - "读取和更改您的书签"

## 临时解决方案

如果扩展有问题，我们可以尝试：

### 方案1：重新加载扩展
1. 在扩展页面点击刷新按钮 🔄
2. 或者先禁用再启用扩展

### 方案2：检查 Native Host 连接
在扩展的后台脚本 Console 中执行：
```javascript
// 测试 Native Messaging
try {
  const port = chrome.runtime.connectNative('com.chromemcp.nativehost');
  console.log('Native port created:', port);
  
  port.onMessage.addListener((message) => {
    console.log('Native message received:', message);
  });
  
  port.onDisconnect.addListener(() => {
    console.log('Native port disconnected:', chrome.runtime.lastError);
  });
  
  port.postMessage({type: 'ping'});
} catch (error) {
  console.error('Native messaging error:', error);
}
```

## 请提供以下信息：
1. 点击 "错误" 按钮后显示的具体错误信息
2. 后台脚本 Console 中的错误详情
3. 扩展图标是否可见和可点击
4. 右键菜单中是否有 MCP 选项
5. 页面 Console 中执行测试代码的结果

根据这些信息，我们可以确定问题所在并修复扩展。
