// MCP工具相关类型定义
export interface MCPTool {
  name: string;
  description: string;
  inputSchema: {
    type: 'object';
    properties: Record<string, SchemaProperty>;
    required?: string[];
  };
}

export interface SchemaProperty {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description?: string;
  items?: SchemaProperty;
  properties?: Record<string, SchemaProperty>;
  enum?: string[];
  default?: any;
}

export interface MCPToolCall {
  toolName: string;
  parameters: Record<string, any>;
  timestamp: number;
  id: string;
}

export interface MCPToolResult {
  success: boolean;
  data?: any;
  error?: string;
  executionTime: number;
  toolName: string;
  callId: string;
}

export interface MCPServerInfo {
  name: string;
  version: string;
  available: boolean;
  endpoint: string;
  lastChecked: number;
}

export interface ToolCategory {
  name: string;
  displayName: string;
  description: string;
  icon: string;
  tools: MCPTool[];
}

export interface ControlPanelConfig {
  serverEndpoint: string;
  autoRefresh: boolean;
  refreshInterval: number;
  theme: 'light' | 'dark' | 'auto';
  compactMode: boolean;
  favoriteTools: string[];
  recentTools: string[];
}