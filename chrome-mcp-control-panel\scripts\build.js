#!/usr/bin/env node
/**
 * 独立控制面板构建脚本
 * 构建Chrome扩展并打包为.zip文件
 */

import { execSync } from 'child_process'
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const rootDir = join(__dirname, '..')

console.log('🚀 开始构建Chrome MCP Control Panel...')

try {
  // 1. 清理构建目录
  console.log('🧹 清理构建目录...')
  if (existsSync(join(rootDir, 'dist'))) {
    execSync('rm -rf dist', { cwd: rootDir })
  }

  // 2. 类型检查
  console.log('🔍 执行TypeScript类型检查...')
  execSync('npm run type-check', { cwd: rootDir, stdio: 'inherit' })

  // 3. 代码检查
  console.log('📋 执行ESLint检查...')
  execSync('npm run lint', { cwd: rootDir, stdio: 'inherit' })

  // 4. 构建扩展
  console.log('🔨 构建扩展文件...')
  execSync('npm run build', { cwd: rootDir, stdio: 'inherit' })

  // 5. 复制图标文件
  console.log('🎨 复制图标文件...')
  const iconsDir = join(rootDir, 'dist', 'icons')
  if (!existsSync(iconsDir)) {
    mkdirSync(iconsDir, { recursive: true })
  }

  // 生成不同尺寸的图标（如果没有的话使用默认图标）
  const sizes = [16, 32, 48, 128]
  sizes.forEach(size => {
    const iconPath = join(rootDir, 'public', 'icons', `icon-${size}.png`)
    if (!existsSync(iconPath)) {
      console.log(`⚠️ 缺少图标: icon-${size}.png，使用占位符`)
      // 这里可以添加生成占位符图标的逻辑
    }
  })

  // 6. 更新manifest版本
  console.log('📝 更新manifest版本...')
  updateManifestVersion()

  // 7. 验证构建结果
  console.log('✅ 验证构建结果...')
  validateBuildResult()

  // 8. 创建发布包
  console.log('📦 创建发布包...')
  createReleasePackage()

  console.log('🎉 构建完成！')
  console.log('📂 构建文件位于: dist/')
  console.log('📦 发布包位于: chrome-mcp-control-panel.zip')

} catch (error) {
  console.error('❌ 构建失败:', error.message)
  process.exit(1)
}

function parseDotEnv(content) {
  const lines = content.split(/\r?\n/)
  const obj = {}
  for (const line of lines) {
    const trimmed = line.trim()
    if (!trimmed || trimmed.startsWith('#')) continue
    const eq = trimmed.indexOf('=')
    if (eq === -1) continue
    const key = trimmed.slice(0, eq).trim()
    let val = trimmed.slice(eq + 1).trim()
    if ((val.startsWith('"') && val.endsWith('"')) || (val.startsWith('\'') && val.endsWith('\''))) {
      val = val.slice(1, -1)
    }
    obj[key] = val
  }
  return obj
}

function updateManifestVersion() {
  const packageJsonPath = join(rootDir, 'package.json')
  const manifestPath = join(rootDir, 'dist', 'manifest.json')
  const envPath = join(rootDir, '.env')
  const fallbackEnvPath = join(rootDir, '..', 'app', 'chrome-extension', '.env')

  if (!existsSync(manifestPath)) {
    throw new Error('manifest.json not found in dist directory')
  }

  const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'))
  const manifest = JSON.parse(readFileSync(manifestPath, 'utf8'))

  // 更新版本号
  manifest.version = packageJson.version

  // 修正 service_worker 路径（确保指向 .js 而非 .ts）
  if (manifest.background && manifest.background.service_worker) {
    manifest.background.service_worker = manifest.background.service_worker.replace(/\.ts$/, '.js')
  }

  // 注入扩展key（若提供）
  let publicKey = process.env.EXTENSION_PUBLIC_KEY
  let privateKey = process.env.EXTENSION_PRIVATE_KEY
  // 1) 本项目根 .env
  if (!(publicKey || privateKey) && existsSync(envPath)) {
    try {
      const envObj = parseDotEnv(readFileSync(envPath, 'utf8'))
      publicKey = envObj.EXTENSION_PUBLIC_KEY || publicKey
      privateKey = envObj.EXTENSION_PRIVATE_KEY || privateKey
    } catch (e) {
      console.warn('⚠️ 读取 .env 失败，跳过 key 注入尝试:', e.message)
    }
  }
  // 2) 尝试从主扩展目录读取（如果存在）
  if (!(publicKey || privateKey) && existsSync(fallbackEnvPath)) {
    try {
      const envObj = parseDotEnv(readFileSync(fallbackEnvPath, 'utf8'))
      publicKey = envObj.EXTENSION_PUBLIC_KEY || envObj.CHROME_EXTENSION_PUBLIC_KEY || publicKey
      privateKey = envObj.EXTENSION_PRIVATE_KEY || envObj.CHROME_EXTENSION_PRIVATE_KEY || privateKey
      if (!publicKey && privateKey) {
        // 从私钥导出公钥（SPKI DER -> base64）
        try {
          const { createPrivateKey } = await import('crypto')
          const key = createPrivateKey(privateKey)
          const der = key.export({ type: 'spki', format: 'der' })
          publicKey = Buffer.from(der).toString('base64')
          console.log('🔑 已从私钥导出公钥用于注入')
        } catch (e) {
          console.warn('⚠️ 私钥导出公钥失败，跳过：', e.message)
        }
      }
    } catch (e) {
      console.warn('⚠️ 读取主扩展 .env 失败，跳过回退注入：', e.message)
    }
  }
  if (publicKey && typeof publicKey === 'string' && publicKey.trim().length > 0) {
    manifest.key = publicKey.trim()
    console.log('🔑 已注入扩展 key (EXTENSION_PUBLIC_KEY)')
  } else {
    console.log('ℹ️ 未检测到扩展 key，跳过注入（扩展ID将不稳定）')
  }

  // 添加构建时间
  const buildTime = new Date().toISOString()
  manifest.build_time = buildTime

  writeFileSync(manifestPath, JSON.stringify(manifest, null, 2))
  console.log(`📝 Updated manifest version to ${packageJson.version}`)
}

function validateBuildResult() {
  const distDir = join(rootDir, 'dist')
  const requiredFiles = [
    'manifest.json',
    'src/background.js',
    'src/pages/popup.html',
    'src/pages/options.html'
  ]

  for (const file of requiredFiles) {
    const filePath = join(distDir, file)
    if (!existsSync(filePath)) {
      throw new Error(`Required file not found: ${file}`)
    }
  }

  console.log('✅ 所有必需文件均存在')
}

function createReleasePackage() {
  const distDir = join(rootDir, 'dist')
  const outputPath = join(rootDir, 'chrome-mcp-control-panel.zip')

  try {
    execSync(`cd "${distDir}" && zip -r "${outputPath}" .`, { stdio: 'inherit' })
  } catch (error) {
    // Fallback for systems without zip command
    console.log('⚠️ zip命令不可用，请手动压缩dist目录')
  }
}
