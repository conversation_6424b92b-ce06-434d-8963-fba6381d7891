import type { MCPTool, SchemaProperty } from '@/types/mcp'
import { reactive, ref, computed } from 'vue'

/**
 * 动态UI生成服务
 * 根据MCP工具的schema自动生成用户界面组件
 */
export class UIGeneratorService {
  private static instance: UIGeneratorService | null = null

  static getInstance(): UIGeneratorService {
    if (!UIGeneratorService.instance) {
      UIGeneratorService.instance = new UIGeneratorService()
    }
    return UIGeneratorService.instance
  }

  /**
   * 根据工具schema生成表单配置
   */
  generateFormConfig(tool: MCPTool): FormConfig {
    const { inputSchema } = tool
    const fields: FormField[] = []

    // 遍历schema属性生成表单字段
    Object.entries(inputSchema.properties || {}).forEach(([key, property]) => {
      const field = this.generateFieldConfig(key, property, inputSchema.required?.includes(key) || false)
      fields.push(field)
    })

    return {
      toolName: tool.name,
      title: this.generateDisplayName(tool.name),
      description: tool.description,
      fields,
      validation: this.generateValidationRules(inputSchema)
    }
  }

  /**
   * 生成单个字段配置
   */
  private generateFieldConfig(key: string, property: SchemaProperty, required: boolean): FormField {
    const baseField: FormField = {
      key,
      label: this.generateFieldLabel(key),
      description: property.description || '',
      required,
      type: this.mapSchemaTypeToUIType(property),
      defaultValue: property.default
    }

    // 根据不同类型添加特定配置
    switch (property.type) {
      case 'string':
        if (property.enum) {
          baseField.type = 'select'
          baseField.options = property.enum.map(value => ({
            label: this.generateOptionLabel(value),
            value
          }))
        } else if (key.includes('url') || key.includes('URL')) {
          baseField.type = 'url'
          baseField.placeholder = 'https://example.com'
        } else if (key.includes('selector')) {
          baseField.type = 'text'
          baseField.placeholder = '.class-name or #element-id'
          baseField.hint = 'CSS选择器格式'
        } else {
          baseField.type = 'text'
        }
        break

      case 'number':
        baseField.type = 'number'
        if (key.includes('width') || key.includes('height')) {
          baseField.placeholder = '像素值'
          baseField.min = 1
          baseField.max = 9999
        } else if (key.includes('timeout')) {
          baseField.placeholder = '毫秒'
          baseField.min = 100
          baseField.max = 60000
        }
        break

      case 'boolean':
        baseField.type = 'switch'
        break

      case 'array':
        baseField.type = 'tags'
        if (property.items?.type === 'string') {
          baseField.placeholder = '输入后按回车添加'
        }
        break

      case 'object':
        baseField.type = 'json'
        baseField.placeholder = '{"key": "value"}'
        break
    }

    // 添加特殊字段的增强功能
    this.addFieldEnhancements(baseField, key, property)

    return baseField
  }

  /**
   * 添加字段增强功能
   */
  private addFieldEnhancements(field: FormField, key: string, property: SchemaProperty): void {
    // 坐标字段
    if (key === 'coordinates') {
      field.type = 'coordinates'
      field.components = ['x', 'y']
      field.hint = '点击页面获取坐标或手动输入'
    }

    // 文件选择字段
    if (key.includes('file') || key.includes('path')) {
      field.type = 'file'
    }

    // 颜色字段
    if (key.includes('color') || key.includes('Color')) {
      field.type = 'color'
    }

    // 时间相关字段
    if (key.includes('time') || key.includes('Time') || key.includes('date')) {
      field.type = 'datetime'
    }

    // 添加快捷预设
    if (this.shouldAddPresets(key)) {
      field.presets = this.generatePresets(key, property)
    }

    // 添加实时验证
    if (this.shouldAddValidation(key)) {
      field.liveValidation = this.generateLiveValidation(key, property)
    }
  }

  /**
   * 生成字段预设选项
   */
  private generatePresets(key: string, property: SchemaProperty): Preset[] {
    const presetMap: Record<string, Preset[]> = {
      'selector': [
        { label: '所有链接', value: 'a' },
        { label: '所有按钮', value: 'button' },
        { label: '所有输入框', value: 'input' },
        { label: '所有图片', value: 'img' }
      ],
      'keys': [
        { label: '回车键', value: 'Enter' },
        { label: '复制', value: 'Ctrl+C' },
        { label: '粘贴', value: 'Ctrl+V' },
        { label: '刷新', value: 'F5' }
      ],
      'method': [
        { label: 'GET请求', value: 'GET' },
        { label: 'POST请求', value: 'POST' },
        { label: 'PUT请求', value: 'PUT' },
        { label: 'DELETE请求', value: 'DELETE' }
      ]
    }

    return presetMap[key] || []
  }

  /**
   * 生成实时验证规则
   */
  private generateLiveValidation(key: string, property: SchemaProperty): ValidationFunction {
    if (key.includes('url') || key.includes('URL')) {
      return (value: string) => {
        if (!value) return { valid: true }
        try {
          new URL(value)
          return { valid: true }
        } catch {
          return { valid: false, message: 'URL格式不正确' }
        }
      }
    }

    if (key.includes('selector')) {
      return (value: string) => {
        if (!value) return { valid: true }
        try {
          document.querySelector(value)
          return { valid: true }
        } catch {
          return { valid: false, message: 'CSS选择器格式不正确' }
        }
      }
    }

    return (value: any) => ({ valid: true })
  }

  /**
   * 映射schema类型到UI组件类型
   */
  private mapSchemaTypeToUIType(property: SchemaProperty): string {
    const typeMap: Record<string, string> = {
      'string': 'text',
      'number': 'number',
      'boolean': 'switch',
      'array': 'tags',
      'object': 'json'
    }

    return typeMap[property.type] || 'text'
  }

  /**
   * 生成显示名称
   */
  private generateDisplayName(toolName: string): string {
    // 移除前缀，转换为友好显示名称
    const name = toolName.replace(/^chrome_/, '').replace(/_/g, ' ')
    return name.split(' ').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  /**
   * 生成字段标签
   */
  private generateFieldLabel(key: string): string {
    // 转换驼峰命名为友好显示
    const label = key.replace(/([A-Z])/g, ' $1').toLowerCase()
    return label.charAt(0).toUpperCase() + label.slice(1)
  }

  /**
   * 生成选项标签
   */
  private generateOptionLabel(value: string): string {
    const labelMap: Record<string, string> = {
      'default': '默认多彩',
      'rainbow': '彩虹色',
      'monochrome': '单色',
      'true': '启用',
      'false': '禁用'
    }

    return labelMap[value] || value
  }

  /**
   * 生成表单验证规则
   */
  private generateValidationRules(schema: any): ValidationRules {
    const rules: ValidationRules = {}

    Object.entries(schema.properties || {}).forEach(([key, property]: [string, any]) => {
      const fieldRules: ValidationRule[] = []

      // 必填验证
      if (schema.required?.includes(key)) {
        fieldRules.push({
          type: 'required',
          message: `${this.generateFieldLabel(key)}是必填项`
        })
      }

      // 类型验证
      if (property.type === 'string' && property.enum) {
        fieldRules.push({
          type: 'enum',
          values: property.enum,
          message: '请选择有效选项'
        })
      }

      if (property.type === 'number') {
        fieldRules.push({
          type: 'number',
          message: '请输入有效数字'
        })
      }

      // 自定义验证
      if (key.includes('url')) {
        fieldRules.push({
          type: 'custom',
          validator: (value: string) => {
            if (!value) return true
            try {
              new URL(value)
              return true
            } catch {
              return false
            }
          },
          message: '请输入有效的URL地址'
        })
      }

      if (fieldRules.length > 0) {
        rules[key] = fieldRules
      }
    })

    return rules
  }

  /**
   * 是否应该添加预设选项
   */
  private shouldAddPresets(key: string): boolean {
    const presetKeys = ['selector', 'keys', 'method', 'colorScheme', 'elementTypes']
    return presetKeys.some(presetKey => key.toLowerCase().includes(presetKey.toLowerCase()))
  }

  /**
   * 是否应该添加实时验证
   */
  private shouldAddValidation(key: string): boolean {
    const validationKeys = ['url', 'selector', 'email', 'json']
    return validationKeys.some(validationKey => key.toLowerCase().includes(validationKey.toLowerCase()))
  }

  /**
   * 生成快速操作按钮
   */
  generateQuickActions(tool: MCPTool): QuickAction[] {
    const actions: QuickAction[] = []

    // 根据工具类型添加快速操作
    if (tool.name.includes('screenshot')) {
      actions.push({
        label: '截取全页',
        icon: 'camera',
        params: { fullPage: true, storeBase64: true }
      })
    }

    if (tool.name.includes('highlight')) {
      actions.push(
        {
          label: '高亮所有按钮',
          icon: 'cursor-click',
          params: { enable: true, elementTypes: ['button'] }
        },
        {
          label: '禁用高亮',
          icon: 'x-mark',
          params: { enable: false }
        }
      )
    }

    if (tool.name.includes('navigate')) {
      actions.push({
        label: '刷新页面',
        icon: 'arrow-path',
        params: { refresh: true }
      })
    }

    return actions
  }
}

// 类型定义
export interface FormConfig {
  toolName: string
  title: string
  description: string
  fields: FormField[]
  validation: ValidationRules
}

export interface FormField {
  key: string
  label: string
  description: string
  type: string
  required: boolean
  defaultValue?: any
  placeholder?: string
  hint?: string
  options?: Option[]
  presets?: Preset[]
  components?: string[]
  min?: number
  max?: number
  liveValidation?: ValidationFunction
}

export interface Option {
  label: string
  value: any
}

export interface Preset {
  label: string
  value: any
}

export interface ValidationRules {
  [key: string]: ValidationRule[]
}

export interface ValidationRule {
  type: 'required' | 'enum' | 'number' | 'custom'
  message: string
  values?: any[]
  validator?: (value: any) => boolean
}

export interface ValidationFunction {
  (value: any): { valid: boolean; message?: string }
}

export interface QuickAction {
  label: string
  icon: string
  params: Record<string, any>
}

export default UIGeneratorService