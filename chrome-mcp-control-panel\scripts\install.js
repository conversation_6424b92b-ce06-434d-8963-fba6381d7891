#!/usr/bin/env node
/**
 * Chrome扩展安装脚本
 * 自动安装构建好的扩展到Chrome浏览器
 */

import { execSync } from 'child_process'
import { existsSync, readFileSync } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'
import { platform } from 'os'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const rootDir = join(__dirname, '..')

console.log('🔧 Chrome MCP Control Panel 安装脚本')

try {
  // 1. 检查构建文件是否存在
  console.log('🔍 检查构建文件...')
  const distDir = join(rootDir, 'dist')
  if (!existsSync(distDir)) {
    throw new Error('构建文件不存在，请先运行 npm run build')
  }

  // 2. 验证manifest文件
  console.log('📋 验证manifest文件...')
  const manifestPath = join(distDir, 'manifest.json')
  if (!existsSync(manifestPath)) {
    throw new Error('manifest.json 不存在')
  }

  const manifest = JSON.parse(readFileSync(manifestPath, 'utf8'))
  console.log(`✅ 扩展名称: ${manifest.name}`)
  console.log(`✅ 扩展版本: ${manifest.version}`)

  // 3. 检查Chrome浏览器
  console.log('🌐 检查Chrome浏览器...')
  const chromeInstalled = checkChromeInstallation()
  if (!chromeInstalled) {
    console.warn('⚠️ 未检测到Chrome浏览器，请手动安装扩展')
    showManualInstallInstructions()
    return
  }

  // 4. 自动安装选项
  console.log('📦 安装选项:')
  console.log('1. 开发者模式安装 (推荐)')
  console.log('2. 生成安装包')
  console.log('3. 显示手动安装说明')

  // 5. 显示安装说明
  showInstallationInstructions()

} catch (error) {
  console.error('❌ 安装失败:', error.message)
  process.exit(1)
}

function checkChromeInstallation() {
  const chromePaths = {
    'win32': [
      'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
      process.env.LOCALAPPDATA + '\\Google\\Chrome\\Application\\chrome.exe'
    ],
    'darwin': [
      '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
    ],
    'linux': [
      '/usr/bin/google-chrome',
      '/usr/bin/google-chrome-stable',
      '/usr/bin/chromium-browser'
    ]
  }

  const currentPlatform = platform()
  const paths = chromePaths[currentPlatform] || []

  for (const path of paths) {
    if (existsSync(path)) {
      console.log(`✅ 找到Chrome: ${path}`)
      return true
    }
  }

  return false
}

function showInstallationInstructions() {
  console.log('\\n📖 安装说明:')
  console.log('════════════════════════════════════════════════════════════════')
  
  console.log('\\n🔧 开发者模式安装 (推荐):')
  console.log('1. 打开Chrome浏览器')
  console.log('2. 访问 chrome://extensions/')
  console.log('3. 开启右上角的"开发者模式"开关')
  console.log('4. 点击"加载已解压的扩展程序"')
  console.log(`5. 选择文件夹: ${join(rootDir, 'dist')}`)
  console.log('6. 扩展将会自动加载并激活')

  console.log('\\n📦 打包安装:')
  console.log('1. 运行: npm run package')
  console.log('2. 生成的.zip文件可用于分发')
  console.log('3. 在Chrome扩展页面拖拽.zip文件安装')

  console.log('\\n🔄 更新扩展:')
  console.log('1. 修改代码后运行: npm run build')
  console.log('2. 在chrome://extensions/页面点击扩展的"刷新"按钮')
  console.log('3. 或者重新加载扩展文件夹')

  console.log('\\n🐛 调试扩展:')
  console.log('1. 在扩展页面点击"检查视图"')
  console.log('2. 使用Chrome DevTools调试background脚本')
  console.log('3. 右键点击扩展图标 -> "检查弹出式窗口"调试popup')

  console.log('\\n⚠️ 注意事项:')
  console.log('- 确保chrome-mcp主扩展正在运行')
  console.log('- 控制面板需要连接到MCP服务器 (http://127.0.0.1:12306)')
  console.log('- 首次使用时会自动尝试连接MCP服务器')
  console.log('- 如果连接失败，请检查主扩展状态')

  console.log('\\n🎯 验证安装:')
  console.log('1. 扩展图标出现在Chrome工具栏')
  console.log('2. 点击图标打开控制面板')
  console.log('3. 状态指示器显示"已连接"')
  console.log('4. 能看到可用工具数量')

  console.log('\\n════════════════════════════════════════════════════════════════')
}

function showManualInstallInstructions() {
  console.log('\\n📖 手动安装说明:')
  console.log('1. 安装Chrome浏览器')
  console.log('2. 按照上述开发者模式安装说明进行操作')
  console.log(`3. 扩展文件位于: ${join(rootDir, 'dist')}`)
}