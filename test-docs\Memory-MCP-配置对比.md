# Memory MCP 配置对比指南

## 📋 概述

本文档记录了在不同 AI 工具和环境中配置 Memory MCP 服务器的完整过程、遇到的问题以及解决方案，为后续配置其他 AI 工具提供参考。

## 🎯 测试环境

- **操作系统**: Windows 11
- **工作目录**: `G:\BaiduSyncdisk\chrome-mcp`
- **数据库路径**: `G:\BaiduSyncdisk\mcp-memory\memory.sqlite`
- **WSL 路径**: `/mnt/g/BaiduSyncdisk/mcp-memory/memory.sqlite`

## 🔧 不同客户端配置对比

### 1. Augment AI

**配置格式**: `mcpServers`
**状态**: ✅ 正常工作

```json
{
  "mcpServers": {
    "mem0-memory": {
      "command": "npx",
      "args": ["-y", "@pinkpixel/mem0-mcp"],
      "env": {
        "MEMORY_DB_PATH": "G:\\BaiduSyncdisk\\mcp-memory\\memory.sqlite"
      }
    }
  }
}
```

**特点**:
- 不需要 `type` 字段
- 使用 `@pinkpixel/mem0-mcp` 包
- Windows 路径格式

### 2. VS Code

**配置格式**: `servers`
**状态**: ✅ 正常工作

```json
{
  "servers": {
    "openmemory": {
      "type": "stdio",
      "command": "npx",
      "args": ["-y", "@peakmojo/mcp-openmemory"],
      "env": {
        "MEMORY_DB_PATH": "G:\\BaiduSyncdisk\\mcp-memory\\memory.sqlite"
      }
    }
  }
}
```

**特点**:
- 必须包含 `"type": "stdio"`
- 使用 `@peakmojo/mcp-openmemory` 包
- Windows 路径格式

### 3. Claude Code CLI (WSL)

**配置格式**: `mcpServers`
**状态**: 🧪 待测试

**主配置**:
```json
{
  "mcpServers": {
    "mem0-memory": {
      "command": "npx",
      "args": ["-y", "@pinkpixel/mem0-mcp"],
      "env": {
        "MEMORY_DB_PATH": "/mnt/g/BaiduSyncdisk/mcp-memory/memory.sqlite"
      }
    }
  }
}
```

**备用配置**:
```json
{
  "mcpServers": {
    "openmemory": {
      "command": "npx",
      "args": ["-y", "openmemory"],
      "env": {
        "MEMORY_DB_PATH": "/mnt/g/BaiduSyncdisk/mcp-memory/memory.sqlite"
      }
    }
  }
}
```

**特点**:
- WSL 路径格式 `/mnt/g/`
- 配置文件位置: `~/.config/claude/claude_desktop_config.json`

## 📊 MCP 包对比

| 包名 | 开发者 | 兼容客户端 | 状态 | 工具数量 |
|------|--------|------------|------|----------|
| `@pinkpixel/mem0-mcp` | pinkpixel | Augment, Claude CLI | ✅ 活跃 | 3+ |
| `@peakmojo/mcp-openmemory` | peakmojo | VS Code | ✅ 稳定 | 3+ |
| `openmemory` | whysosaket | 通用 | ✅ 简化版 | 基础 |

## 🛠️ 工具命令对比

### @pinkpixel/mem0-mcp
```bash
# 添加记忆
add-memory
# 示例: "请使用 add-memory 工具保存：用户喜欢使用中文进行交流"

# 搜索记忆
search-memories
# 示例: "请使用 search-memories 工具搜索关于用户语言偏好的信息"

# 获取所有记忆
get-all-memories
# 示例: "请使用 get-all-memories 工具获取所有保存的记忆"
```

### @peakmojo/mcp-openmemory
```bash
# 保存记忆
save_memory
# 示例: "请使用 save_memory 工具保存：用户正在测试MCP功能"

# 获取记忆摘要
recall_memory_abstract
# 示例: "请使用 recall_memory_abstract 工具获取当前记忆摘要"

# 更新记忆摘要
update_memory_abstract
# 示例: "请使用 update_memory_abstract 工具更新记忆摘要"
```

### 通用测试命令
```bash
# 不确定工具名称时，让AI自动选择
"请帮我保存一个记忆：我正在配置MCP记忆服务器"
"请搜索关于MCP配置的记忆"
"请获取所有记忆的摘要"
```

## 🚨 常见问题与解决方案

### 问题 1: "不允许属性 mcpServers"
**原因**: VS Code 使用 `"servers"` 而不是 `"mcpServers"`
**解决**: 将 `"mcpServers"` 改为 `"servers"`

### 问题 2: "Process exited with code 1"
**原因**: MCP 服务器启动后异常退出
**解决**:
1. 检查包是否正确安装
2. 尝试不同的 MCP 包
3. 检查环境变量设置

### 问题 3: "Connection closed"
**原因**: MCP 连接中断
**解决**:
1. 重启 MCP 服务器
2. 检查配置文件格式
3. 验证数据库路径

## 📁 配置文件位置

| 客户端 | 配置文件路径 |
|--------|--------------|
| **Augment** | 项目根目录 `.vscode/mcp.json` |
| **VS Code** | 项目根目录 `.vscode/mcp.json` |
| **Claude CLI (WSL)** | `~/.config/claude/claude_desktop_config.json` |
| **Claude Desktop** | `%APPDATA%\Claude\claude_desktop_config.json` |

## 🔄 迁移步骤

### 从 @peakmojo 迁移到 @pinkpixel

1. **备份数据库**:
   ```bash
   copy "G:\BaiduSyncdisk\mcp-memory\memory.sqlite" "backup.sqlite"
   ```

2. **安装新包**:
   ```bash
   npm install -g @pinkpixel/mem0-mcp
   ```

3. **更新配置文件**:
   - 修改 `args` 中的包名
   - 保持数据库路径不变

4. **重启 MCP 服务器**

## 💡 最佳实践

1. **统一数据库路径**: 所有客户端使用相同的数据库文件
2. **备份配置**: 保存工作配置的备份
3. **测试兼容性**: 新环境先测试基础功能
4. **文档记录**: 记录每个环境的工作配置

## 🎯 推荐配置

### 生产环境推荐
- **Augment**: `@pinkpixel/mem0-mcp`
- **VS Code**: `@peakmojo/mcp-openmemory`
- **Claude CLI**: `@pinkpixel/mem0-mcp`

### 开发测试推荐
- **通用**: `openmemory` (简化版，快速测试)

## 🚀 快速安装指南

### 安装 MCP 包
```bash
# 安装 @pinkpixel/mem0-mcp (推荐用于 Augment 和 Claude CLI)
npm install -g @pinkpixel/mem0-mcp

# 安装 @peakmojo/mcp-openmemory (推荐用于 VS Code)
npm install -g @peakmojo/mcp-openmemory

# 安装 openmemory (轻量级版本)
npm install -g openmemory
```

### 创建数据库目录
```bash
# Windows
mkdir "G:\BaiduSyncdisk\mcp-memory"

# WSL
mkdir -p /mnt/g/BaiduSyncdisk/mcp-memory
```

## 🧪 测试验证

### VS Code 测试步骤
1. 重启 MCP 服务器: `Ctrl+Shift+P` → `MCP: List Servers` → 重启
2. 进入 Agent 模式: `Ctrl+Alt+I` → 选择 "Agent mode"
3. 测试命令: `#recall_memory_abstract`

### Augment 测试步骤
1. 检查 MCP 连接状态
2. 测试命令: `#add-memory`
3. 验证记忆保存: `#search-memories`

### Claude CLI 测试步骤
```bash
# 在 WSL 中
claude "请帮我保存一个记忆：MCP配置测试成功"
claude "请搜索关于MCP的记忆"
```

## 📈 性能对比

| 特性 | @pinkpixel/mem0-mcp | @peakmojo/mcp-openmemory | openmemory |
|------|---------------------|--------------------------|------------|
| **启动速度** | 中等 | 快速 | 最快 |
| **功能丰富度** | 高 | 中等 | 基础 |
| **稳定性** | 良好 | 优秀 | 良好 |
| **文档完整性** | 中等 | 有限 | 基础 |

## 📝 更新日志

- **2025-08-11 17:20**: 初始版本，记录三个客户端的配置过程
- **2025-08-11 17:20**: 添加详细的工具命令对比和示例
- **2025-08-11 17:20**: 补充安装指南和测试步骤
- **待更新**: Claude Code CLI 实际测试结果

## 🔗 相关资源

- [Mem0 官方文档](https://docs.mem0.ai)
- [MCP 协议规范](https://modelcontextprotocol.io)
- [VS Code MCP 扩展文档](https://code.visualstudio.com/docs/copilot/chat/mcp-servers)

---

*本文档将持续更新，记录新的配置经验和问题解决方案。如有问题或建议，请及时更新此文档。*
