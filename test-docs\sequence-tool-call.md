# 工具调用链时序（以 chrome_screenshot 为例）

推荐查看：VS Code / Obsidian / GitHub Web（Markdown + Mermaid）。

## 时序图（Mermaid）
```mermaid
sequenceDiagram
  participant Client as MCP 客户端
  participant Fastify as 原生服务器/Fastify
  participant MCP as MCP Server
  participant Reg as 工具注册路由
  participant Host as 原生消息主机
  participant BG as 扩展背景页
  participant Tool as screenshotTool
  participant CS as 内容脚本(screenshot-helper)
  participant Chrome as Chrome APIs

  Client->>Fastify: POST /mcp (callTool: chrome_screenshot)
  Fastify->>MCP: transport.handleRequest(...)
  MCP->>Reg: setRequestHandler(CallTool)
  Reg->>Host: sendRequestToExtensionAndWait(\n  type=CALL_TOOL, requestId\n)
  Host->>BG: Native Messaging postMessage
  BG->>Tool: handleCallTool({name,args})
  Tool->>Chrome: chrome.tabs.query / permissions 检查
  Tool->>CS: chrome.scripting.executeScript(screenshot-helper.js)
  Tool->>CS: sendMessage(preparePageForCapture)
  Tool->>Chrome: captureVisibleTab / 下载 API
  Tool-->>BG: CallToolResult (content[], isError=false)
  BG-->>Host: responseToRequestId
  Host-->>Reg: resolve(payload)
  Reg-->>Client: MCP CallToolResult
```

## 关键实现位置
- 注册与转发：
  - app/native-server/src/mcp/register-tools.ts（handleToolCall → CALL_TOOL）
  - app/native-server/src/native-messaging-host.ts（sendRequestToExtensionAndWait）
  - app/chrome-extension/entrypoints/background/native-host.ts（CALL_TOOL → handleCallTool）
- 工具执行：
  - app/chrome-extension/entrypoints/background/tools/browser/screenshot.ts
  - 内容脚本：app/chrome-extension/inject-scripts/screenshot-helper.js

## 结果结构（CallToolResult）
- content: [{ type: 'text' | 'image', ... }]
- isError: boolean

## 查看建议
- VS Code 内置 Mermaid 预览扩展或 Obsidian 渲染最佳。

