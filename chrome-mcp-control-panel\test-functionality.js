/**
 * Chrome MCP Control Panel 功能测试脚本
 * 用于验证所有核心功能是否正常工作
 */

console.log('🧪 开始Chrome MCP Control Panel功能测试...')

// 测试1: 检查构建文件
function testBuildFiles() {
  console.log('\n📁 测试1: 检查构建文件...')
  
  const fs = require('fs')
  const path = require('path')
  
  const requiredFiles = [
    'dist/manifest.json',
    'dist/src/background.js',
    'dist/src/pages/popup.html',
    'dist/src/pages/options.html',
    'dist/popup.js',
    'dist/options.js'
  ]
  
  let allFilesExist = true
  
  requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file} - 存在`)
    } else {
      console.log(`❌ ${file} - 缺失`)
      allFilesExist = false
    }
  })
  
  return allFilesExist
}

// 测试2: 检查manifest配置
function testManifest() {
  console.log('\n📋 测试2: 检查manifest配置...')
  
  const fs = require('fs')
  const manifest = JSON.parse(fs.readFileSync('dist/manifest.json', 'utf8'))
  
  const requiredFields = [
    'manifest_version',
    'name',
    'version',
    'permissions',
    'background',
    'action'
  ]
  
  let manifestValid = true
  
  requiredFields.forEach(field => {
    if (manifest[field]) {
      console.log(`✅ ${field} - 配置正确`)
    } else {
      console.log(`❌ ${field} - 配置缺失`)
      manifestValid = false
    }
  })
  
  // 检查权限
  const requiredPermissions = ['storage', 'activeTab', 'tabs', 'scripting']
  requiredPermissions.forEach(perm => {
    if (manifest.permissions.includes(perm)) {
      console.log(`✅ 权限 ${perm} - 已配置`)
    } else {
      console.log(`❌ 权限 ${perm} - 缺失`)
      manifestValid = false
    }
  })
  
  return manifestValid
}

// 测试3: 检查图标文件
function testIcons() {
  console.log('\n🎨 测试3: 检查图标文件...')
  
  const fs = require('fs')
  const iconSizes = [16, 32, 48, 128]
  let iconsValid = true
  
  iconSizes.forEach(size => {
    const iconPath = `public/icons/icon-${size}.svg`
    if (fs.existsSync(iconPath)) {
      console.log(`✅ icon-${size}.svg - 存在`)
    } else {
      console.log(`❌ icon-${size}.svg - 缺失`)
      iconsValid = false
    }
  })
  
  return iconsValid
}

// 测试4: 检查核心服务文件
function testServices() {
  console.log('\n🔧 测试4: 检查核心服务文件...')
  
  const fs = require('fs')
  const serviceFiles = [
    'src/services/mcpClient.ts',
    'src/services/chromeExtensionBridge.ts',
    'src/services/connectionManager.ts',
    'src/services/uiGenerator.ts'
  ]
  
  let servicesValid = true
  
  serviceFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8')
      if (content.length > 1000) { // 检查文件不是空的
        console.log(`✅ ${file} - 存在且有内容`)
      } else {
        console.log(`⚠️ ${file} - 存在但内容较少`)
      }
    } else {
      console.log(`❌ ${file} - 缺失`)
      servicesValid = false
    }
  })
  
  return servicesValid
}

// 测试5: 检查Vue组件
function testComponents() {
  console.log('\n🎭 测试5: 检查Vue组件...')
  
  const fs = require('fs')
  const componentFiles = [
    'src/components/OptionsApp.vue',
    'src/components/PopupApp.vue',
    'src/components/WebPageController.vue'
  ]
  
  let componentsValid = true
  
  componentFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8')
      if (content.includes('<template>') && content.includes('<script>')) {
        console.log(`✅ ${file} - Vue组件结构完整`)
      } else {
        console.log(`⚠️ ${file} - Vue组件结构可能不完整`)
      }
    } else {
      console.log(`❌ ${file} - 缺失`)
      componentsValid = false
    }
  })
  
  return componentsValid
}

// 测试6: 检查样式文件
function testStyles() {
  console.log('\n🎨 测试6: 检查样式文件...')
  
  const fs = require('fs')
  const styleFiles = [
    'src/assets/styles/popup.css',
    'src/assets/styles/options.css'
  ]
  
  let stylesValid = true
  
  styleFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8')
      if (content.length > 1000) { // 检查样式文件有足够内容
        console.log(`✅ ${file} - 样式完整`)
      } else {
        console.log(`⚠️ ${file} - 样式内容较少`)
      }
    } else {
      console.log(`❌ ${file} - 缺失`)
      stylesValid = false
    }
  })
  
  return stylesValid
}

// 运行所有测试
function runAllTests() {
  const tests = [
    { name: '构建文件', fn: testBuildFiles },
    { name: 'Manifest配置', fn: testManifest },
    { name: '图标文件', fn: testIcons },
    { name: '核心服务', fn: testServices },
    { name: 'Vue组件', fn: testComponents },
    { name: '样式文件', fn: testStyles }
  ]
  
  let allPassed = true
  const results = []
  
  tests.forEach(test => {
    try {
      const result = test.fn()
      results.push({ name: test.name, passed: result })
      if (!result) allPassed = false
    } catch (error) {
      console.log(`❌ ${test.name} - 测试失败: ${error.message}`)
      results.push({ name: test.name, passed: false })
      allPassed = false
    }
  })
  
  // 输出测试总结
  console.log('\n📊 测试结果总结:')
  console.log('='.repeat(50))
  
  results.forEach(result => {
    const status = result.passed ? '✅ 通过' : '❌ 失败'
    console.log(`${result.name}: ${status}`)
  })
  
  console.log('='.repeat(50))
  
  if (allPassed) {
    console.log('🎉 所有测试通过！Chrome MCP Control Panel 已准备就绪！')
    console.log('\n📝 下一步操作:')
    console.log('1. 在Chrome中加载扩展: chrome://extensions/')
    console.log('2. 启动chrome-mcp-server插件或MCP服务器')
    console.log('3. 点击扩展图标开始使用')
  } else {
    console.log('⚠️ 部分测试失败，请检查上述错误并修复')
  }
  
  return allPassed
}

// 如果直接运行此脚本
if (require.main === module) {
  runAllTests()
}

module.exports = { runAllTests }