/**
 * Chrome扩展后台服务
 * 负责扩展生命周期管理和MCP客户端维护
 */
import { MCPClient } from './services/mcpClient'
import ChromeExtensionBridge from './services/chromeExtensionBridge'

let mcpClient: MCPClient | null = null
let extensionBridge: ChromeExtensionBridge | null = null

// 扩展安装或更新时初始化
chrome.runtime.onInstalled.addListener(async (_details) => {
  console.log('🚀 Chrome MCP Control Panel 已安装/更新')

  // 初始化默认配置
  await chrome.storage.local.set({
    config: {
      serverEndpoint: 'http://127.0.0.1:12306',
      autoRefresh: true,
      refreshInterval: 30000,
      theme: 'auto',
      compactMode: false,
      favoriteTools: [],
      recentTools: []
    }
  })

  // 配置侧边栏默认页面（Chrome 116+）
  try {
    const sp = (chrome as any).sidePanel
    if (sp?.setOptions) {
      await sp.setOptions({ path: 'src/pages/sidebar.html', enabled: true })
    }
  } catch (e) {
    console.log('配置 sidePanel 失败（可忽略）', e)
  }

  // 初始化MCP客户端
  await initializeMCPClient()
})

// 扩展启动时初始化
chrome.runtime.onStartup.addListener(async () => {
  console.log('🔄 Chrome MCP Control Panel 启动')
  await initializeMCPClient()
})

// 点击图标时打开侧边栏（需要 sidePanel 权限）
chrome.action.onClicked.addListener(async (tab) => {
  try {
    const sidePanel = (chrome as any).sidePanel
    if (sidePanel?.open) {
      await sidePanel.open({ windowId: tab?.windowId })
    } else {
      // 兼容无 sidePanel API 的环境：回退打开 popup 页
      chrome.tabs.create({ url: chrome.runtime.getURL('src/pages/popup.html') })
    }
  } catch (e) {
    console.log('打开侧边栏失败', e)
  }
})

// 初始化MCP客户端（尝试扩展模式，其次直连模式）
async function initializeMCPClient() {
  try {
    const { config } = await chrome.storage.local.get(['config'])

    // 优先尝试连接chrome-mcp-server插件
    if (!extensionBridge) extensionBridge = ChromeExtensionBridge.getInstance()

    try {
      const serverInfo = await extensionBridge.detectAndConnect()
      console.log('✅ 成功连接到chrome-mcp-server插件')

      // 更新扩展徽章（绿色：扩展模式）
      const toolsCount = extensionBridge.getToolsCount()
      chrome.action.setBadgeText({ text: toolsCount.toString() })
      chrome.action.setBadgeBackgroundColor({ color: '#22c55e' })
      chrome.action.setTitle({ title: `MCP控制面板 - ${toolsCount}个工具可用 (插件模式)` })

      // 保存服务器信息
      await chrome.storage.local.set({
        serverInfo,
        connectionMode: 'extension'
      })

      return
    } catch (extensionError: any) {
      console.log('🔄 chrome-mcp-server插件连接失败，尝试直连模式:', extensionError?.message || extensionError)
    }

    // 如果插件连接失败，尝试直连MCP服务器
    mcpClient = MCPClient.getInstance(config?.serverEndpoint)
    const serverInfo = await mcpClient.initialize()

    // 更新扩展徽章（蓝色：直连模式；红色：未连上）
    if (serverInfo.available) {
      chrome.action.setBadgeText({ text: mcpClient.getToolsCount().toString() })
      chrome.action.setBadgeBackgroundColor({ color: '#3b82f6' })
      chrome.action.setTitle({ title: `MCP控制面板 - ${mcpClient.getToolsCount()}个工具可用 (直连模式)` })
    } else {
      chrome.action.setBadgeText({ text: '!' })
      chrome.action.setBadgeBackgroundColor({ color: '#ef4444' })
      chrome.action.setTitle({ title: 'MCP控制面板 - 服务器未连接' })
    }

    // 保存服务器信息
    await chrome.storage.local.set({
      serverInfo,
      connectionMode: 'direct'
    })
  } catch (error) {
    console.error('❌ MCP客户端初始化失败:', error)

    chrome.action.setBadgeText({ text: '✗' })
    chrome.action.setBadgeBackgroundColor({ color: '#ef4444' })
    chrome.action.setTitle({ title: 'MCP控制面板 - 初始化失败' })
  }
}

// 懒加载：在收到消息或需要使用时确保已初始化
async function ensureInitialized() {
  try {
    // 若扩展模式已连接，直接返回
    if (extensionBridge && extensionBridge.isConnected()) return
    // 若直连模式已连接，直接返回
    if (mcpClient && mcpClient.isConnected()) return
    // 否则尝试初始化
    await initializeMCPClient()
  } catch (e) {
    // 忽略，交由调用方根据返回状态判断
  }
}

// 监听来自popup和options页面的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  handleMessage(request, sender, sendResponse)
  return true // 保持消息通道开放以支持异步响应
})

// 处理各种消息（优先扩展模式，其次直连模式；必要时懒初始化）
async function handleMessage(request: any, _sender: chrome.runtime.MessageSender, sendResponse: Function) {
  try {
    await ensureInitialized()

    switch (request.action) {
      case 'getMCPStatus': {
        const extConnected = extensionBridge?.isConnected() || false
        const extTools = extConnected ? extensionBridge!.getToolsCount() : 0
        const dirConnected = mcpClient?.isConnected() || false
        const dirTools = dirConnected ? mcpClient!.getToolsCount() : 0
        const connected = extConnected || dirConnected
        const toolsCount = extConnected ? extTools : dirTools
        const { serverInfo } = await chrome.storage.local.get(['serverInfo'])

        sendResponse({
          success: true,
          data: {
            connected,
            toolsCount,
            serverInfo: serverInfo || { available: false }
          }
        })
        break
      }

      case 'getTools': {
        if (extensionBridge?.isConnected()) {
          const tools = extensionBridge.getAllTools()
          const categorizedTools = extensionBridge.getToolsByCategory()
          sendResponse({ success: true, data: { tools, categorizedTools, totalCount: tools.length } })
          break
        }
        if (mcpClient?.isConnected()) {
          const tools = mcpClient.getAllTools()
          const categorizedTools = mcpClient.getToolsByCategory()
          sendResponse({ success: true, data: { tools, categorizedTools, totalCount: tools.length } })
          break
        }
        sendResponse({ success: false, error: 'MCP客户端未连接' })
        break
      }

      case 'callTool': {
        if (extensionBridge?.isConnected()) {
          const result = await extensionBridge.callTool(request.toolName, request.parameters)
          await recordRecentTool(request.toolName)
          sendResponse({ success: true, data: result })
          break
        }
        if (mcpClient?.isConnected()) {
          const result = await mcpClient.callTool(request.toolName, request.parameters)
          await recordRecentTool(request.toolName)
          sendResponse({ success: true, data: result })
          break
        }
        sendResponse({ success: false, error: 'MCP客户端未连接' })
        break
      }

      case 'refreshTools': {
        if (extensionBridge?.isConnected()) {
          const refreshedTools = await extensionBridge.refreshTools()
          chrome.action.setBadgeText({ text: refreshedTools.length.toString() })
          sendResponse({ success: true, data: { tools: refreshedTools, count: refreshedTools.length } })
          break
        }
        if (mcpClient?.isConnected()) {
          const refreshedTools = await mcpClient.refreshTools()
          chrome.action.setBadgeText({ text: refreshedTools.length.toString() })
          sendResponse({ success: true, data: { tools: refreshedTools, count: refreshedTools.length } })
          break
        }
        sendResponse({ success: false, error: 'MCP客户端未连接' })
        break
      }

      case 'reconnect':
        await initializeMCPClient()
        sendResponse({ success: true })
        break

      case 'updateConfig': {
        const { config } = request
        await chrome.storage.local.set({ config })
        // 如果服务器端点变更，重新连接（仅影响直连模式）
        if (config.serverEndpoint !== mcpClient?.getEndpoint()) {
          await initializeMCPClient()
        }
        sendResponse({ success: true })
        break
      }

      case 'getConfig': {
        const { config: currentConfig } = await chrome.storage.local.get(['config'])
        sendResponse({ success: true, data: currentConfig })
        break
      }

      case 'addFavoriteTool':
        await toggleFavoriteTool(request.toolName, true)
        sendResponse({ success: true })
        break

      case 'removeFavoriteTool':
        await toggleFavoriteTool(request.toolName, false)
        sendResponse({ success: true })
        break

      case 'getFavoriteTools': {
        const { config: favConfig } = await chrome.storage.local.get(['config'])
        sendResponse({ success: true, data: favConfig?.favoriteTools || [] })
        break
      }

      case 'getRecentTools': {
        const { config: recentConfig } = await chrome.storage.local.get(['config'])
        sendResponse({ success: true, data: recentConfig?.recentTools || [] })
        break
      }

      default:
        sendResponse({ success: false, error: '未知操作' })
    }
  } catch (error) {
    console.error('❌ 消息处理失败:', error)
    sendResponse({ success: false, error: error instanceof Error ? error.message : '未知错误' })
  }
}

// 记录最近使用的工具
async function recordRecentTool(toolName: string) {
  const { config } = await chrome.storage.local.get(['config'])
  const recentTools = config?.recentTools || []

  // 移除重复项并添加到首位
  const updatedRecent = [toolName, ...recentTools.filter((tool: string) => tool !== toolName)].slice(0, 10)

  await chrome.storage.local.set({
    config: {
      ...config,
      recentTools: updatedRecent
    }
  })
}

// 切换收藏工具
async function toggleFavoriteTool(toolName: string, add: boolean) {
  const { config } = await chrome.storage.local.get(['config'])
  const favoriteTools = config?.favoriteTools || []

  let updatedFavorites
  if (add) {
    updatedFavorites = [...new Set([...favoriteTools, toolName])]
  } else {
    updatedFavorites = favoriteTools.filter((tool: string) => tool !== toolName)
  }

  await chrome.storage.local.set({
    config: {
      ...config,
      favoriteTools: updatedFavorites
    }
  })
}

// 定期检查MCP连接状态
setInterval(async () => {
  if (mcpClient && !mcpClient.isConnected()) {
    console.log('🔄 MCP连接断开，尝试重新连接...')
    await initializeMCPClient()
  }
}, 60000) // 每分钟检查一次

// 监听工具更新事件
addEventListener('mcp-tools-updated', (event: any) => {
  const toolsCount = event.detail.tools.length
  chrome.action.setBadgeText({ text: toolsCount.toString() })
  console.log(`📡 工具列表已更新，当前${toolsCount}个工具`)
})

export {}
