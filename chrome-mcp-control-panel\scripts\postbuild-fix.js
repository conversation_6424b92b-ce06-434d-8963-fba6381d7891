// Post-build fix: ensure dist/manifest.json has background.service_worker pointing to src/background.js
// Some environments of vite-plugin-web-extension may leave .ts in manifest
// This script normalizes it to .js for Chrome loading.

import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';

const distManifest = join(process.cwd(), 'dist', 'manifest.json');
if (!existsSync(distManifest)) {
  console.error('[postbuild-fix] manifest not found:', distManifest);
  process.exit(0);
}

try {
  const json = readFileSync(distManifest, 'utf8');
  const fixed = json.replace(/("service_worker"\s*:\s*")src\/background\.ts(")/g, '$1src/background.js$2');
  if (fixed !== json) {
    writeFileSync(distManifest, fixed, 'utf8');
    console.log('[postbuild-fix] Patched service_worker to src/background.js');
  } else {
    console.log('[postbuild-fix] No change needed');
  }
} catch (e) {
  console.error('[postbuild-fix] error:', e);
}

