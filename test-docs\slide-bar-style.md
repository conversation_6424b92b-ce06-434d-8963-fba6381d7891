

Chrome MCP 控制面板 UI 设计
HTML 结构
HTML

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome MCP 控制面板</title>
    <link rel="stylesheet" href="sidebar.css">
</head>
<body>
    <div class="sidebar-container">
        <!-- 顶部标题栏 -->
        <header class="header">
            <div class="header-content">
                <div class="logo-section">
                    <svg class="logo" width="24" height="24" viewBox="0 0 24 24">
                        <circle cx="12" cy="12" r="10" fill="#007AFF"/>
                        <path d="M9 12l2 2 4-4" stroke="white" stroke-width="2" fill="none"/>
                    </svg>
                    <h1>Chrome MCP</h1>
                </div>
                <button class="settings-btn">
                    <svg width="20" height="20" viewBox="0 0 20 20">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" fill="currentColor"/>
                        <path d="M10 4a6 6 0 100 12 6 6 0 000-12zm0 10a4 4 0 110-8 4 4 0 010 8z" fill="currentColor"/>
                    </svg>
                </button>
            </div>
        </header>

        <!-- 功能区域 -->
        <div class="content-area">
            <!-- 功能分组 -->
            <div class="function-groups">
                <!-- 截图功能组 -->
                <div class="function-group">
                    <h2 class="group-title">
                        <svg class="icon" width="16" height="16" viewBox="0 0 16 16">
                            <rect x="2" y="4" width="12" height="8" rx="1" fill="none" stroke="currentColor"/>
                            <circle cx="8" cy="8" r="2" fill="currentColor"/>
                        </svg>
                        截图功能
                    </h2>
                    <div class="function-items">
                        <div class="function-item">
                            <div class="item-header">
                                <span class="item-title">页面截图</span>
                                <label class="switch">
                                    <input type="checkbox" id="screenshot-enable">
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="item-options">
                                <div class="option-row">
                                    <label>保存位置</label>
                                    <div class="file-input">
                                        <input type="text" placeholder="C:\Users\<USER>\Users\Screenshots\2024-01-15_143022.png</span>
                        <span class="feedback-time">14:30:22</span>
                    </div>
                    <div class="feedback-item info">
                        <svg class="status-icon" width="16" height="16">
                            <circle cx="8" cy="8" r="8" fill="#007AFF"/>
                            <text x="8" y="12" text-anchor="middle" fill="white" font-size="10">i</text>
                        </svg>
                        <span class="feedback-text">网络监控已开启，正在捕获请求...</span>
                        <span class="feedback-time">14:29:15</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部输入区 -->
        <footer class="input-area">
            <h3 class="area-title">命令输入</h3>
            <div class="command-input">
                <input type="text" placeholder="输入命令或参数，如: chrome_screenshot {&quot;format&quot;: &quot;png&quot;}" id="command-input">
                <button class="send-btn">
                    <svg width="20" height="20" viewBox="0 0 20 20">
                        <path d="M2 10l16-8v16L2 10z" fill="currentColor"/>
                    </svg>
                </button>
            </div>
            <div class="quick-commands">
                <button class="quick-cmd">截图</button>
                <button class="quick-cmd">获取内容</button>
                <button class="quick-cmd">清除缓存</button>
                <button class="quick-cmd">导出数据</button>
            </div>
        </footer>
    </div>
</body>
</html>
CSS 样式 (sidebar.css)
CSS

/* Apple 风格的基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Segoe UI', Roboto, Arial, sans-serif;
    font-size: 14px;
    color: #1d1d1f;
    background: #f5f5f7;
    line-height: 1.47;
}

.sidebar-container {
    width: 400px;
    height: 100vh;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.08);
}

/* 头部样式 */
.header {
    background: #ffffff;
    border-bottom: 1px solid #e5e5e7;
    padding: 16px 20px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo {
    flex-shrink: 0;
}

h1 {
    font-size: 18px;
    font-weight: 600;
    color: #1d1d1f;
}

.settings-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    color: #86868b;
    transition: all 0.2s;
}

.settings-btn:hover {
    background: #f5f5f7;
    color: #1d1d1f;
}

/* 内容区域 */
.content-area {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

/* 功能组样式 */
.function-group {
    margin-bottom: 24px;
}

.group-title {
    font-size: 16px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.icon {
    color: #007AFF;
}

.function-items {
    background: #f5f5f7;
    border-radius: 12px;
    padding: 16px;
}

.function-item {
    background: #ffffff;
    border-radius: 10px;
    padding: 16px;
    margin-bottom: 12px;
}

.function-item:last-child {
    margin-bottom: 0;
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.item-title {
    font-weight: 500;
    color: #1d1d1f;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 28px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #e5e5e7;
    transition: .3s;
    border-radius: 28px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 24px;
    width: 24px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
    background-color: #34C759;
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* 选项样式 */
.item-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.option-row {
    display: flex;
    align-items: center;
    gap: 12px;
}

.option-row label {
    font-size: 13px;
    color: #86868b;
    min-width: 80px;
}

/* 输入框样式 */
.text-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #e5e5e7;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: all 0.2s;
}

.text-input:focus {
    border-color: #007AFF;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.text-input::placeholder {
    color: #c7c7cc;
}

.text-input.full {
    width: 100%;
}

/* 文件输入样式 */
.file-input {
    display: flex;
    flex: 1;
    gap: 8px;
}

.file-input input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #e5e5e7;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
}

.browse-btn {
    padding: 8px 16px;
    background: #f5f5f7;
    border: 1px solid #e5e5e7;
    border-radius: 8px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s;
}

.browse-btn:hover {
    background: #e5e5e7;
}

/* 选择框样式 */
.select-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #e5e5e7;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    background: white;
    cursor: pointer;
}

/* 单选框组 */
.radio-group {
    display: flex;
    gap: 16px;
}

.radio {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
}

.radio input {
    width: 16px;
    height: 16px;
    accent-color: #007AFF;
}

.radio span {
    font-size: 14px;
    color: #1d1d1f;
}

/* 复选框组 */
.checkbox-group {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.checkbox {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
}

.checkbox input {
    width: 16px;
    height: 16px;
    accent-color: #007AFF;
}

.checkbox span {
    font-size: 14px;
    color: #1d1d1f;
}

/* 样式按钮组 */
.style-options {
    display: flex;
    gap: 8px;
}

.style-btn {
    padding: 6px 12px;
    background: #f5f5f7;
    border: 1px solid #e5e5e7;
    border-radius: 6px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s;
}

.style-btn:hover {
    background: #e5e5e7;
}

.style-btn.active {
    background: #007AFF;
    color: white;
    border-color: #007AFF;
}

/* 操作按钮 */
.action-btn {
    padding: 10px 20px;
    background: #f5f5f7;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    margin-top: 12px;
}

.action-btn:hover {
    background: #e5e5e7;
}

.action-btn.primary {
    background: #007AFF;
    color: white;
}

.action-btn.primary:hover {
    background: #0051D5;
}

/* 徽章样式 */
.badge {
    padding: 2px 8px;
    background: #FF3B30;
    color: white;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
}

/* 反馈区域 */
.feedback-area {
    margin-top: 24px;
    background: #f5f5f7;
    border-radius: 12px;
    padding: 16px;
}

.area-title {
    font-size: 14px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 12px;
}

.feedback-content {
    max-height: 200px;
    overflow-y: auto;
}

.feedback-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 8px;
    background: white;
    border-radius: 8px;
    margin-bottom: 8px;
}

.feedback-item:last-child {
    margin-bottom: 0;
}

.status-icon {
    flex-shrink: 0;
}

.feedback-text {
    flex: 1;
    font-size: 13px;
    color: #1d1d1f;
}

.feedback-time {
    font-size: 12px;
    color: #86868b;
}

/* 底部输入区 */
.input-area {
    background: #ffffff;
    border-top: 1px solid #e5e5e7;
    padding: 16px 20px;
}

.command-input {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.command-input input {
    flex: 1;
    padding: 10px 16px;
    border: 1px solid #e5e5e7;
    border-radius: 20px;
    font-size: 14px;
    outline: none;
}

.command-input input:focus {
    border-color: #007AFF;
}

.send-btn {
    width: 40px;
    height: 40px;
    background: #007AFF;
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.send-btn:hover {
    background: #0051D5;
}

.quick-commands {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.quick-cmd {
    padding: 6px 12px;
    background: #f5f5f7;
    border: 1px solid #e5e5e7;
    border-radius: 16px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s;
}

.quick-cmd:hover {
    background: #e5e5e7;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: #c7c7cc;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #86868b;
}
JavaScript 交互逻辑 (sidebar.js)
JavaScript

// Chrome MCP 控制面板交互逻辑
class ChromeMCPController {
    constructor() {
        this.initializeEventListeners();
        this.loadSavedSettings();
    }

    // 初始化事件监听器
    initializeEventListeners() {
        // 截图功能
        document.getElementById('screenshot-enable').addEventListener('change', (e) => {
            this.toggleScreenshot(e.target.checked);
        });

        // 网络监控
        document.getElementById('network-capture').addEventListener('change', (e) => {
            this.toggleNetworkCapture(e.target.checked);
        });

        // 元素高亮
        document.getElementById('element-highlight').addEventListener('change', (e) => {
            this.toggleElementHighlight(e.target.checked);
        });

        // 命令输入
        document.getElementById('command-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.executeCommand(e.target.value);
                e.target.value = '';
            }
        });

        // 快速命令按钮
        document.querySelectorAll('.quick-cmd').forEach(btn => {
            btn.addEventListener('click', () => {
                this.executeQuickCommand(btn.textContent);
            });
        });

        // 样式按钮切换
        document.querySelectorAll('.style-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.style-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });

        // 文件浏览按钮
        document.querySelectorAll('.browse-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.browseFolder();
            });
        });
    }

    // 加载保存的设置
    loadSavedSettings() {
        chrome.storage.local.get(['mcpSettings'], (result) => {
            if (result.mcpSettings) {
                this.applySettings(result.mcpSettings);
            }
        });
    }

    // 应用设置
    applySettings(settings) {
        if (settings.screenshotPath) {
            document.getElementById('screenshot-path').value = settings.screenshotPath;
        }
        // 应用其他设置...
    }

    // 切换截图功能
    toggleScreenshot(enabled) {
        this.sendMessage({
            action: 'toggleFeature',
            feature: 'screenshot',
            enabled: enabled
        });
        this.addFeedback(`截图功能已${enabled ? '开启' : '关闭'}`, 'info');
    }

    // 切换网络监控
    toggleNetworkCapture(enabled) {
        this.sendMessage({
            action: 'toggleFeature',
            feature: 'networkCapture',
            enabled: enabled
        });
        this.addFeedback(`网络监控已${enabled ? '开启' : '关闭'}`, 'info');
    }

    // 切换元素高亮
    toggleElementHighlight(enabled) {
        const borderStyle = document.querySelector('.style-btn.active').textContent;
        this.sendMessage({
            action: 'chrome_show_page_elements',
            params: {
                show: enabled,
                borderStyle: borderStyle === '实线' ? 'solid' : 
                           borderStyle === '虚线' ? 'dashed' : 'dotted'
            }
        });
        this.addFeedback(`元素高亮已${enabled ? '开启' : '关闭'}`, 'info');
    }

    // 执行命令
    executeCommand(command) {
        try {
            // 解析命令
            if (command.startsWith('{')) {
                // JSON 格式的命令
                const cmdObj = JSON.parse(command);
                this.sendMessage(cmdObj);
            } else {
                // 文本格式的命令
                this.sendMessage({
                    action: 'executeCommand',
                    command: command
                });
            }
            this.addFeedback(`执行命令: ${command}`, 'info');
        } catch (error) {
            this.addFeedback(`命令格式错误: ${error.message}`, 'error');
        }
    }

    // 执行快速命令
    executeQuickCommand(commandName) {
        const commands = {
            '截图': { action: 'chrome_screenshot', params: { format: 'png' } },
            '获取内容': { action: 'chrome_get_content' },
            '清除缓存': { action: 'chrome_clear_cache' },
            '导出数据': { action: 'chrome_export_data' }
        };

        const command = commands[commandName];
        if (command) {
            this.sendMessage(command);
            this.addFeedback(`执行快速命令: ${commandName}`, 'info');
        }
    }

    // 发送消息到 Chrome MCP
    sendMessage(message) {
        // 与您的控制面板通信逻辑
        chrome.runtime.sendMessage({
            target: 'chrome-mcp',
            message: message
        }, (response) => {
            if (response && response.success) {
                this.addFeedback(response.message || '操作成功', 'success');
            } else {
                this.addFeedback(response.error || '操作失败', 'error');
            }
        });
    }

    // 添加反馈信息
    addFeedback(message, type) {
        const feedbackContent = document.querySelector('.feedback-content');
        const feedbackItem = document.createElement('div');
        feedbackItem.className = `feedback-item ${type}`;
        
        const time = new Date().toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit' 
        });

        const iconColors = {
            success: '#34C759',
            error: '#FF3B30',
            info: '#007AFF',
            warning: '#FF9500'
        };

        feedbackItem.innerHTML = `
            <svg class="status-icon" width="16" height="16">
                <circle cx="8" cy="8" r="8" fill="${iconColors[type] || iconColors.info}"/>
                ${type === 'success' ? '<path d="M5 8l2 2 4-4" stroke="white" stroke-width="2" fill="none"/>' :
                  type === 'error' ? '<path d="M5 5l6 6M11 5l-6 6" stroke="white" stroke-width="2"/>' :
                  '<text x="8" y="12" text-anchor="middle" fill="white" font-size="10">i</text>'}
            </svg>
            <span class="feedback-text">${message}</span>
            <span class="feedback-time">${time}</span>
        `;

        feedbackContent.insertBefore(feedbackItem, feedbackContent.firstChild);
        
        // 限制反馈数量
        while (feedbackContent.children.length > 10) {
            feedbackContent.removeChild(feedbackContent.lastChild);
        }
    }

    // 浏览文件夹
    browseFolder() {
        // 使用 Chrome API 或发送消息到主扩展来选择文件夹
        this.sendMessage({
            action: 'browseFolder'
        });
    }

    // 保存设置
    saveSettings() {
        const settings = {
            screenshotPath: document.getElementById('screenshot-path').value,
            // 收集其他设置...
        };

        chrome.storage.local.set({ mcpSettings: settings }, () => {
            this.addFeedback('设置已保存', 'success');
        });
    }
}

// 初始化控制器
document.addEventListener('DOMContentLoaded', () => {
    new ChromeMCPController();
});
使用说明
文件结构：

text

chrome-sidebar-extension/
├── sidebar.html
├── sidebar.css
├── sidebar.js
└── manifest.json
在 manifest.json 中添加侧边栏配置：

JSON

{
  "manifest_version": 3,
  "name": "Chrome MCP 控制面板",
  "side_panel": {
    "default_path": "sidebar.html"
  },
  "permissions": [
    "storage",
    "sidePanel"
  ]
}
与 chrome-mcp 通信： 在您已完成的通信部分基础上，使用 sendMessage 方法发送控制命令。

特色功能
Apple 风格设计：

圆润的边角和阴影
优雅的动画过渡
清晰的视觉层次
用户友好的交互：

滑动开关控制
灰色占位符提示
文件浏览器集成
快速命令按钮
实时反馈系统：

彩色状态图标
时间戳显示
自动清理旧消息
灵活的命令输入：

支持 JSON 格式
快速命令按钮
回车键发送
这个设计提供了一个完整的、美观的、易用的控制面板，可以让用户轻松控制 chrome-mcp 的所有功能。您可以根据实际需求进一步调整和扩展功能。