# z-mcp-chrome 架构与通信关系总览

本文档总结了项目核心组件、通信协议、数据流向与错误传播路径，并给出组件级 Mermaid 图示。推荐查看方式：
- VS Code 或 Obsidian（可原生渲染 Mermaid）
- GitHub Web 也可直接查看 Markdown 与 Mermaid（可能存在渲染延迟）

## 主要组件与职责
- 原生服务器（app/native-server）
  - Fastify HTTP 服务：/mcp（Streamable HTTP）、/sse（SSE）、/messages（SSE回传）
  - MCP Server（@modelcontextprotocol/sdk）会话/传输管理
  - 工具注册与路由：register-tools.ts 将调用转发至原生消息主机
- 原生消息主机（app/native-server/src/native-messaging-host.ts）
  - 通过 Chrome Native Messaging（stdin/stdout + 4字节长度头）与扩展通信
  - requestId/responseToRequestId 关联应答，含超时控制与挂起表（pendingRequests）
- Chrome 扩展（app/chrome-extension）
  - 背景页：native-host.ts 连接原生主机；tools/* 执行浏览器工具
  - 内容脚本：inject-scripts/*.js 承担页面上下文交互
  - 离屏文档：offscreen/main.ts 负责语义引擎/嵌入/向量搜索
  - 弹窗：popup/* 提供状态与配置 UI
- 共享包（packages/shared）
  - NativeMessageType、TOOL_SCHEMAS 等协议与工具清单

## 组件通信图（Mermaid）
```mermaid
flowchart TB
  subgraph AI[AI/MCP 客户端]
    Claude[Claude / 其它 MCP 客户端]
  end

  subgraph Server[原生服务器 (app/native-server)]
    Fastify[Fastify HTTP 服务器\n端点: /mcp (Streamable HTTP), /sse (SSE)\n端口: 127.0.0.1:12306]
    MCPServer[MCP SDK Server\ngetMcpServer()]
    Transports[传输层\nStreamableHTTPServerTransport\nSSEServerTransport]
    Registry[工具注册与分发\nregister-tools.ts]
  end

  subgraph NativeHost[原生消息主机 (native-messaging-host.ts)]
    Pending[请求路由与超时\npendingRequests Map (requestId/resolve/reject/timeout)]
  end

  subgraph Ext[Chrome 扩展 (app/chrome-extension)]
    BG[背景页 background\nindex.ts / native-host.ts]
    Tools[工具执行器\nbackground/tools/*]
    CS[内容脚本\ninject-scripts/*.js]
    Offscreen[离屏文档\noffscreen/main.ts\n语义引擎/向量搜索]
    Popup[弹窗 UI\npopup/App.vue]
  end

  subgraph Browser[浏览器能力与存储]
    ChromeAPIs[Chrome APIs\nbookmarks/history/debugger/webRequest\n scripting/tabs/runtime]
    Storage[chrome.storage.local]
    IndexedDB[IndexedDB\n向量索引/模型缓存]
  end

  Shared[共享包 packages/shared\nNativeMessageType, TOOL_SCHEMAS]

  Claude -->|HTTP POST /mcp\nGET/DELETE /mcp (SSE)\n协议: Streamable HTTP / SSE| Fastify
  Fastify --> MCPServer
  MCPServer --> Transports
  MCPServer --> Registry
  Registry -->|NativeMessageType.CALL_TOOL\nsendRequestToExtensionAndWait(requestId)| Pending
  Pending -->|Chrome Native Messaging\nstdin/stdout + 4字节长度头| BG

  BG -->|chrome.runtime.connectNative\npostMessage / responseToRequestId| Pending
  BG -->|按 name 分发| Tools
  Tools -->|chrome.scripting.executeScript\nchrome.tabs.sendMessage| CS
  Tools -->|直接调用| ChromeAPIs
  Tools -->|消息 (OFFSCREEN_MESSAGE_TYPES)| Offscreen
  Offscreen --> Storage
  Offscreen --> IndexedDB
  CS -->|页面交互/DOM| ChromeAPIs

  Tools -->|CallToolResult| BG
  BG -->|responseToRequestId| Pending
  Pending -->|MCP 响应 (CallToolResult)| MCPServer
  MCPServer --> Claude

  Shared --> Registry
  Shared --> BG

  Pending -.->|超时/解析失败\nreject(Error)| MCPServer
  Fastify -.->|400/500\nINVALID_MCP_REQUEST 等| Claude
  BG -.->|TOOL_EXECUTION_FAILED| Pending
  Offscreen -.->|UPDATE_MODEL_STATUS 错误状态| BG
```

## 数据流简述
- 工具调用：客户端 → /mcp → MCPServer → register-tools → NativeHost（CALL_TOOL）→ 扩展背景页 → 对应工具 → 内容脚本/Chrome APIs/离屏文档 → 结果回传
- SSE/流式：支持 /sse + /messages 与 Streamable HTTP（/mcp + 事件流 GET /mcp）
- 原生消息：长度前缀 + JSON；以 requestId/responseToRequestId 关联

## 错误传播
- 工具执行异常 → 扩展回传 {status:'error', error:...} → MCP 转换为 CallToolResult.isError=true
- 原生消息超时 → NativeHost reject(Error) → MCP 以错误内容回包
- MCP 层校验失败 → Fastify 返回 400/500，含 INVALID_MCP_REQUEST 等

更多细节请见 test-docs/interfaces.md、test-docs/sequence-tool-call.md 与 test-docs/sequence-errors.md。
