import type { <PERSON><PERSON><PERSON><PERSON>, MCP<PERSON>oolResult, MCPServerInfo } from '@/types/mcp'

/**
 * MCP服务客户端 - 与chrome-mcp服务器通信
 * 实现自适应工具发现和动态调用机制
 */
export class MCPClient {
  private static instance: MCPClient | null = null
  private endpoint: string
  private tools: Map<string, MCPTool> = new Map()
  private sessionId: string | null = null
  private connected: boolean = false
  private eventSource: EventSource | null = null

  constructor(endpoint: string = 'http://127.0.0.1:12306') {
    this.endpoint = endpoint
  }

  static getInstance(endpoint?: string): MCPClient {
    if (!MCPClient.instance) {
      MCPClient.instance = new MCPClient(endpoint)
    }
    return MCPClient.instance
  }

  /**
   * 初始化MCP连接并获取可用工具列表
   */
  async initialize(): Promise<MCPServerInfo> {
    // 候选端点（优先使用传入/配置的endpoint，其次尝试常见端点）
    const candidates = Array.from(
      new Set([
        this.endpoint,
        'http://127.0.0.1:12306',
        'http://localhost:12306',
      ])
    )

    for (const ep of candidates) {
      try {
        // 1. 建立HTTP连接并初始化会话
        const initResponse = await fetch(`${ep}/mcp`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/event-stream'
          },
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: '1',
            method: 'initialize',
            params: {
              protocolVersion: '2024-11-05',
              capabilities: { tools: {} },
              clientInfo: { name: 'ChromeMcpControlPanel', version: '1.0.0' }
            }
          })
        })

        if (!initResponse.ok) {
          throw new Error(`Failed to initialize MCP connection: ${initResponse.status}`)
        }

        const initResult = await initResponse.json()
        this.sessionId = initResponse.headers.get('mcp-session-id')
        this.connected = true
        this.endpoint = ep

        // 2. 获取服务器信息
        const serverInfo = initResult.result?.serverInfo || {
          name: 'ChromeMcpServer',
          version: '1.0.0'
        }

        // 3. 获取可用工具列表
        await this.refreshTools()

        // 4. 设置工具监听（如果服务器支持）
        await this.setupToolListener()

        console.log(`🔌 MCP客户端已连接到 ${this.endpoint}`)
        console.log(`📋 发现 ${this.tools.size} 个可用工具`)

        return {
          name: serverInfo.name,
          version: serverInfo.version,
          available: true,
          endpoint: this.endpoint,
          lastChecked: Date.now()
        }
      } catch (err) {
        // 尝试下一个候选端点
        console.warn(`连接端点失败，尝试下一个: ${ep}`, err)
        this.connected = false
        this.sessionId = null
      }
    }

    // 所有候选端点均失败（属预期降级场景，使用 warn 避免在扩展详情页显示红色错误）
    console.warn('⚠️ MCP连接失败: 所有候选端点均不可用')
    return {
      name: 'ChromeMcpServer',
      version: 'Unknown',
      available: false,
      endpoint: this.endpoint,
      lastChecked: Date.now()
    }
  }

  /**
   * 刷新工具列表 - 实现自适应工具发现
   */
  async refreshTools(): Promise<MCPTool[]> {
    if (!this.connected || !this.sessionId) {
      throw new Error('MCP客户端未连接')
    }

    try {
      const response = await fetch(`${this.endpoint}/mcp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'mcp-session-id': this.sessionId
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: '2',
          method: 'tools/list',
          params: {}
        })
      })

      if (!response.ok) {
        throw new Error(`获取工具列表失败: ${response.status}`)
      }

      const result = await response.json()
      const toolsList = result.result?.tools || []

      // 更新工具缓存
      this.tools.clear()
      toolsList.forEach((tool: MCPTool) => {
        this.tools.set(tool.name, tool)
      })

      console.log(`🔄 工具列表已更新，共 ${this.tools.size} 个工具`)

      return Array.from(this.tools.values())

    } catch (error) {
      console.error('❌ 获取工具列表失败:', error)
      throw error
    }
  }

  /**
   * 调用MCP工具
   */
  async callTool(toolName: string, parameters: Record<string, any>): Promise<MCPToolResult> {
    if (!this.connected || !this.sessionId) {
      throw new Error('MCP客户端未连接')
    }

    if (!this.tools.has(toolName)) {
      throw new Error(`未找到工具: ${toolName}`)
    }

    const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const startTime = Date.now()

    try {
      const response = await fetch(`${this.endpoint}/mcp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'mcp-session-id': this.sessionId
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: callId,
          method: 'tools/call',
          params: {
            name: toolName,
            arguments: parameters
          }
        })
      })

      const result = await response.json()
      const executionTime = Date.now() - startTime

      if (result.error) {
        return {
          success: false,
          error: result.error.message || '工具执行失败',
          executionTime,
          toolName,
          callId
        }
      }

      return {
        success: true,
        data: result.result,
        executionTime,
        toolName,
        callId
      }

    } catch (error) {
      const executionTime = Date.now() - startTime

      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        executionTime,
        toolName,
        callId
      }
    }
  }

  /**
   * 设置工具监听 - 实现实时同步
   */
  private async setupToolListener(): Promise<void> {
    try {
      // 如果服务器支持SSE，建立事件流连接
      this.eventSource = new EventSource(`${this.endpoint}/sse`)

      this.eventSource.addEventListener('tools_updated', (event) => {
        console.log('📡 检测到工具更新，正在刷新...')
        this.refreshTools().catch(console.error)
      })

      this.eventSource.addEventListener('error', (event) => {
        console.warn('⚠️ 工具监听连接异常:', event)
      })

    } catch (error) {
      // 如果不支持SSE，使用轮询机制
      console.log('📡 使用轮询模式监听工具更新')
      this.startPolling()
    }
  }

  /**
   * 启动轮询监听机制
   */
  private startPolling(): void {
    setInterval(async () => {
      try {
        const currentToolsCount = this.tools.size
        await this.refreshTools()

        if (this.tools.size !== currentToolsCount) {
          // 工具数量发生变化，触发更新事件
          (globalThis as any).dispatchEvent(
            new CustomEvent('mcp-tools-updated', {
              detail: { tools: Array.from(this.tools.values()) }
            })
          )
        }
      } catch (error) {
        console.error('⚠️ 工具轮询更新失败:', error)
      }
    }, 30000) // 每30秒检查一次
  }

  /**
   * 获取工具定义
   */
  getTool(toolName: string): MCPTool | undefined {
    return this.tools.get(toolName)
  }

  /**
   * 获取所有工具
   */
  getAllTools(): MCPTool[] {
    return Array.from(this.tools.values())
  }

  /**
   * 按分类获取工具
   */
  getToolsByCategory(): Record<string, MCPTool[]> {
    const categories: Record<string, MCPTool[]> = {}

    this.tools.forEach(tool => {
      // 根据工具名称前缀分类
      const prefix = tool.name.split('_')[0] || 'general'
      const category = this.getCategoryDisplayName(prefix)

      if (!categories[category]) {
        categories[category] = []
      }
      categories[category].push(tool)
    })

    return categories
  }

  /**
   * 获取分类显示名称
   */
  private getCategoryDisplayName(prefix: string): string {
    const categoryMap: Record<string, string> = {
      'chrome': '浏览器控制',
      'get': '信息获取',
      'search': '搜索功能',
      'network': '网络请求',
      'bookmark': '书签管理',
      'history': '历史记录',
      'console': '调试工具'
    }

    return categoryMap[prefix] || '其他工具'
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close()
      this.eventSource = null
    }

    this.connected = false
    this.sessionId = null
    this.tools.clear()

    console.log('🔌 MCP客户端已断开连接')
  }

  /**
   * 获取连接状态
   */
  isConnected(): boolean {
    return this.connected
  }

  /**
   * 获取工具数量
   */
  getToolsCount(): number {
    return this.tools.size
  }

  /**
   * 获取当前端点
   */
  getEndpoint(): string {
    return this.endpoint
  }
}
