# 接口签名与协议细节（z-mcp-chrome）

推荐查看：VS Code / Obsidian / GitHub Web（Markdown + Mermaid）。

## 1. HTTP / MCP 端点（Fastify）
- POST /mcp
  - 用途：客户端 → 服务器的 MCP 请求（含 initialize 与 callTool 等）
  - 头部：可包含 mcp-session-id；若缺失且 body 为 initialize，则由服务器生成
  - 传输：StreamableHTTPServerTransport.handleRequest(request, reply, body)
- GET /mcp
  - 用途：SSE 事件通道（服务器 → 客户端）
  - 要求：Header 携带 mcp-session-id，配合 Streamable HTTP 会话
- DELETE /mcp
  - 用途：关闭会话
  - 要求：Header 携带 mcp-session-id
- GET /sse
  - 用途：SSE 初始化
  - 返回：SSEServerTransport('/messages')
- POST /messages?sessionId=...
  - 用途：SSE 数据回传

对应代码：
- app/native-server/src/server/index.ts（setupRoutes）
- app/native-server/src/mcp/mcp-server.ts（getMcpServer）

## 2. MCP 层能力与消息
- 服务器：@modelcontextprotocol/sdk Server
  - capabilities.tools: {}（动态由 register-tools 注册）
- 工具注册：app/native-server/src/mcp/register-tools.ts
  - ListToolsRequest → 返回 packages/shared/src/tools.ts 的 TOOL_SCHEMAS
  - CallToolRequest(name, arguments) → handleToolCall(name, args)

CallToolResult（扩展返回后的统一结构）：
- content: Array<TextContent | ImageContent>
- isError: boolean

## 3. 原生消息（Chrome Native Messaging）
- 管道：stdin/stdout；每条消息前置 4 字节小端长度头 + UTF-8 JSON 字节
- 请求/响应绑定：
  - 请求：{ type, payload, requestId }
  - 响应：{ responseToRequestId, payload | error }
- 超时：sendRequestToExtensionAndWait(payload, type, timeoutMs)
  - pendingRequests: Map<requestId, {resolve, reject, timeoutId}>
  - 超时触发 reject(Error)

消息类型（packages/shared/src/types.ts → NativeMessageType）：
- START/STOP：控制 Fastify 启停
- CALL_TOOL / CALL_TOOL_RESPONSE：工具调用链
- PROCESS_DATA（示例 ping）
- SERVER_STARTED / SERVER_STOPPED / ERROR_FROM_NATIVE_HOST
- CONNECT_NATIVE / PING_NATIVE / DISCONNECT_NATIVE（扩展侧辅助）

对应代码：
- app/native-server/src/native-messaging-host.ts
- app/chrome-extension/entrypoints/background/native-host.ts
- packages/shared/src/types.ts

## 4. 扩展内部消息类型
common/message-types.ts：
- BACKGROUND_MESSAGE_TYPES：
  - SWITCH_SEMANTIC_MODEL / GET_MODEL_STATUS / UPDATE_MODEL_STATUS / GET_STORAGE_STATS / CLEAR_ALL_DATA / GET_SERVER_STATUS / REFRESH_SERVER_STATUS / INITIALIZE_SEMANTIC_ENGINE
- OFFSCREEN_MESSAGE_TYPES：
  - SIMILARITY_ENGINE_INIT / SIMILARITY_ENGINE_COMPUTE / SIMILARITY_ENGINE_BATCH_COMPUTE / SIMILARITY_ENGINE_STATUS
- TOOL_MESSAGE_TYPES：
  - 截图：preparePageForCapture / getPageDetails / getElementDetails / scrollPage / resetPageAfterCapture
  - 抓取：getHtmlContent / getTextContent
  - 交互：clickElement / fillElement / simulateKeyboard
  - 调试/网络：sendPureNetworkRequest 等

## 5. 工具清单与输入模式
- 来源：packages/shared/src/tools.ts（TOOL_SCHEMAS，含 name/description/inputSchema）
- 示例（节选）：
  - chrome_screenshot
    - 输入：{ name, selector?, width?, height?, storeBase64?, fullPage?, savePng? }
  - chrome_navigate
    - 输入：{ url?, newWindow?, width?, height?, refresh? }
  - chrome_network_debugger_start/stop, chrome_network_capture_start/stop

## 6. 典型请求-响应样例
- 原生消息请求：
```json
{
  "type": "call_tool",
  "payload": {"name": "chrome_screenshot", "args": {"storeBase64": true}},
  "requestId": "e2a9a2c0-..."
}
```
- 扩展响应：
```json
{
  "responseToRequestId": "e2a9a2c0-...",
  "payload": {
    "status": "success",
    "message": "Tool executed successfully",
    "data": {"content": [{"type":"text","text":"..."}], "isError": false}
  }
}
```
- MCP 回包（CallToolResult）：
```json
{
  "content": [{"type": "text", "text": "{\"success\":true,...}"}],
  "isError": false
}
```

## 7. 推荐查看 APP
- VS Code：Markdown 与 Mermaid 渲染友好
- Obsidian：适合知识库阅读
- GitHub Web：在线预览（Mermaid 渲染受网络影响）

