// vite.config.ts
import { defineConfig } from "file:///G:/BaiduSyncdisk/chrome-mcp/chrome-mcp-control-panel/node_modules/.pnpm/vite@5.4.19_@types+node@20.19.10/node_modules/vite/dist/node/index.js";
import vue from "file:///G:/BaiduSyncdisk/chrome-mcp/chrome-mcp-control-panel/node_modules/.pnpm/@vitejs+plugin-vue@4.6.2_vi_bb267634889fab0652839c1c2ec5e8ea/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import { resolve } from "path";
import webExtension from "file:///G:/BaiduSyncdisk/chrome-mcp/chrome-mcp-control-panel/node_modules/.pnpm/vite-plugin-web-extension@4.4.5_@types+node@20.19.10/node_modules/vite-plugin-web-extension/dist/index.js";
var __vite_injected_original_dirname = "G:\\BaiduSyncdisk\\chrome-mcp\\chrome-mcp-control-panel";
var vite_config_default = defineConfig({
  plugins: [
    vue(),
    webExtension({
      manifest: "./public/manifest.json",
      watchFilePaths: ["src/**/*"],
      additionalInputs: [
        "src/pages/options.html",
        "src/pages/popup.html",
        "src/pages/sidebar.html"
      ]
    })
  ],
  resolve: {
    alias: {
      "@": resolve(__vite_injected_original_dirname, "src"),
      "@/components": resolve(__vite_injected_original_dirname, "src/components"),
      "@/pages": resolve(__vite_injected_original_dirname, "src/pages"),
      "@/utils": resolve(__vite_injected_original_dirname, "src/utils"),
      "@/services": resolve(__vite_injected_original_dirname, "src/services"),
      "@/types": resolve(__vite_injected_original_dirname, "src/types")
    }
  },
  build: {
    outDir: "dist"
  },
  server: {
    port: 3e3,
    host: "localhost"
  },
  define: {
    __VUE_OPTIONS_API__: false,
    __VUE_PROD_DEVTOOLS__: false
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
