/* Chrome MCP Control Panel - Popup样式 */

/* 全局样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  line-height: 1.4;
}

/* 容器样式 */
.popup-container {
  width: 400px;
  min-height: 500px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot.connected {
  background: #10b981;
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.2);
}

.status-dot.disconnected {
  background: #ef4444;
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.2);
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

.options-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 6px;
  padding: 6px;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.options-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 工具统计 */
.tools-summary {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.summary-card {
  text-align: center;
}

.summary-number {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.summary-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

/* 快速操作 */
.quick-actions {
  padding: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 12px;
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 12px;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  color: #64748b;
}

.action-button:hover {
  border-color: #667eea;
  background: #f1f5f9;
  transform: translateY(-1px);
}

.action-icon {
  width: 24px;
  height: 24px;
  stroke-width: 1.5;
}

.action-button span {
  font-size: 12px;
  font-weight: 500;
}

/* 最近使用工具 */
.recent-tools {
  padding: 20px;
  border-top: 1px solid #e2e8f0;
}

.tool-list {
  space-y: 8px;
}

.tool-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.tool-item:hover {
  background: #e2e8f0;
}

.tool-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.tool-arrow {
  width: 16px;
  height: 16px;
  color: #9ca3af;
}

/* 连接错误 */
.connection-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  min-height: 300px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.connection-error h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.connection-error p {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 24px;
}

.reconnect-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s;
}

.reconnect-button:hover {
  transform: translateY(-1px);
}

/* 底部操作 */
.footer {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.footer-button {
  flex: 1;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.footer-button.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.footer-button.secondary {
  background: white;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.footer-button:hover {
  transform: translateY(-1px);
}

/* 动画 */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 响应式调整 */
@media (max-width: 400px) {
  .popup-container {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
  }
  
  .action-grid {
    grid-template-columns: 1fr;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}