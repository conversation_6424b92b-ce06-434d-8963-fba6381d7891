MCP Memory 配置经验记录 - 2025-08-11

=== 核心配置经验 ===

1. 客户端配置差异：
   - Augment: "mcpServers" + @pinkpixel/mem0-mcp
   - VS Code: "servers" + "type": "stdio" + @peakmojo/mcp-openmemory  
   - Claude CLI: "mcpServers" + WSL路径格式

2. 数据库统一路径：
   - Windows: G:\BaiduSyncdisk\mcp-memory\memory.sqlite
   - WSL: /mnt/g/BaiduSyncdisk/mcp-memory/memory.sqlite

3. 工具命令对比：
   - @pinkpixel/mem0-mcp: add-memory, search-memories, get-all-memories
   - @peakmojo/mcp-openmemory: save_memory, recall_memory_abstract, update_memory_abstract

4. 常见问题解决：
   - "不允许属性 mcpServers" → 改为 "servers"
   - "Process exited with code 1" → 检查包安装和环境变量
   - "Connection closed" → 重启MCP服务器

5. 最佳实践：
   - 统一数据库路径确保数据一致性
   - 备份工作配置
   - 测试基础功能后再扩展
   - 记录每个环境的成功配置

=== 待测试项目 ===
- Claude Code CLI 在 WSL 环境下的实际表现
- 不同包之间的数据兼容性
- 性能对比测试

=== 配置文件位置 ===
- 详细配置文档: test-docs/Memory-MCP-配置对比.md
- VS Code配置: .vscode/mcp.json
- Claude CLI配置: ~/.config/claude/claude_desktop_config.json
