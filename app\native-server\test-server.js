#!/usr/bin/env node

// 简单的测试服务器启动脚本
const { Server } = require('./dist/server/index.js');
const { NativeMessagingHost } = require('./dist/native-messaging-host.js');
const { NATIVE_SERVER_PORT } = require('./dist/constant/index.js');

async function startTestServer() {
  try {
    console.log('🚀 启动测试 MCP 服务器...');
    
    // 创建服务器和 Native Host 实例
    const server = new Server();
    const nativeHost = new NativeMessagingHost();
    
    // 设置关联
    server.setNativeHost(nativeHost);
    nativeHost.setServer(server);
    
    // 启动服务器
    await server.start(NATIVE_SERVER_PORT, nativeHost);
    
    console.log(`✅ MCP 服务器已启动在端口 ${NATIVE_SERVER_PORT}`);
    console.log(`🔗 MCP 端点: http://127.0.0.1:${NATIVE_SERVER_PORT}/mcp`);
    console.log('按 Ctrl+C 停止服务器');
    
    // 处理退出信号
    process.on('SIGINT', async () => {
      console.log('\n🛑 正在停止服务器...');
      try {
        await server.stop();
        console.log('✅ 服务器已停止');
        process.exit(0);
      } catch (error) {
        console.error('❌ 停止服务器时出错:', error);
        process.exit(1);
      }
    });
    
  } catch (error) {
    console.error('❌ 启动服务器失败:', error);
    process.exit(1);
  }
}

startTestServer();
