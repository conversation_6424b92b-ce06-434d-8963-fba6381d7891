<template>
  <div class="webpage-controller">
    <!-- 当前页面信息 -->
    <div class="page-info-section">
      <h3 class="section-title">
        <span class="title-icon">🌐</span>
        当前页面控制
      </h3>
      
      <div v-if="currentPageInfo" class="page-info">
        <div class="page-header">
          <img :src="currentPageInfo.favicon" class="page-favicon" alt="favicon">
          <div class="page-details">
            <h4 class="page-title">{{ currentPageInfo.title }}</h4>
            <p class="page-url">{{ currentPageInfo.url }}</p>
          </div>
          <button @click="refreshPageInfo" class="refresh-btn" title="刷新页面信息">
            🔄
          </button>
        </div>
      </div>
    </div>

    <!-- 快速操作面板 -->
    <div class="quick-actions-section">
      <h4 class="subsection-title">快速操作</h4>
      <div class="quick-actions-grid">
        <button 
          v-for="action in quickActions" 
          :key="action.id"
          @click="executeQuickAction(action)"
          :class="['quick-action-btn', action.category]"
          :disabled="isExecuting"
        >
          <span class="action-icon">{{ action.icon }}</span>
          <span class="action-label">{{ action.label }}</span>
        </button>
      </div>
    </div>

    <!-- 工具开关面板 -->
    <div class="tools-toggle-section">
      <h4 class="subsection-title">工具开关</h4>
      <div class="tools-grid">
        <div 
          v-for="tool in categorizedTools" 
          :key="tool.name"
          class="tool-toggle-card"
        >
          <div class="tool-header">
            <div class="tool-info">
              <span class="tool-icon">{{ getToolIcon(tool.name) }}</span>
              <span class="tool-name">{{ formatToolName(tool.name) }}</span>
            </div>
            <label class="toggle-switch">
              <input 
                type="checkbox" 
                :checked="enabledTools.includes(tool.name)"
                @change="toggleTool(tool.name, $event)"
              >
              <span class="toggle-slider"></span>
            </label>
          </div>
          <p class="tool-description">{{ tool.description }}</p>
          
          <!-- 工具参数快速设置 -->
          <div v-if="enabledTools.includes(tool.name)" class="tool-params">
            <div 
              v-for="param in getQuickParams(tool)" 
              :key="param.key"
              class="param-control"
            >
              <label class="param-label">{{ param.label }}</label>
              <input 
                v-if="param.type === 'text'"
                v-model="toolParams[tool.name][param.key]"
                type="text"
                :placeholder="param.placeholder"
                class="param-input"
              >
              <input 
                v-else-if="param.type === 'number'"
                v-model.number="toolParams[tool.name][param.key]"
                type="number"
                :min="param.min"
                :max="param.max"
                class="param-input"
              >
              <select 
                v-else-if="param.type === 'select'"
                v-model="toolParams[tool.name][param.key]"
                class="param-select"
              >
                <option value="">请选择...</option>
                <option 
                  v-for="option in param.options" 
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </option>
              </select>
            </div>
            
            <button 
              @click="executeToolWithParams(tool.name)"
              class="execute-tool-btn"
              :disabled="isExecuting"
            >
              {{ isExecuting ? '执行中...' : '执行' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 执行结果显示 -->
    <div v-if="lastExecutionResult" class="execution-result">
      <h4 class="subsection-title">执行结果</h4>
      <div :class="['result-content', lastExecutionResult.success ? 'success' : 'error']">
        <div class="result-header">
          <span class="result-icon">
            {{ lastExecutionResult.success ? '✅' : '❌' }}
          </span>
          <span class="result-tool">{{ lastExecutionResult.toolName }}</span>
          <span class="result-time">{{ formatExecutionTime(lastExecutionResult.executionTime) }}</span>
        </div>
        <div class="result-data">
          <pre v-if="lastExecutionResult.data">{{ JSON.stringify(lastExecutionResult.data, null, 2) }}</pre>
          <p v-if="lastExecutionResult.error" class="error-message">{{ lastExecutionResult.error }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue'
import type { MCPTool, MCPToolResult } from '@/types/mcp'
import ChromeExtensionBridge from '@/services/chromeExtensionBridge'

// 响应式数据
const currentPageInfo = ref<any>(null)
const enabledTools = ref<string[]>([])
const toolParams = reactive<Record<string, Record<string, any>>>({})
const isExecuting = ref(false)
const lastExecutionResult = ref<MCPToolResult | null>(null)

// 服务实例
const extensionBridge = ChromeExtensionBridge.getInstance()

// 快速操作定义
const quickActions = ref([
  {
    id: 'screenshot',
    icon: '📸',
    label: '截图',
    category: 'capture',
    action: 'takeScreenshot'
  },
  {
    id: 'scroll-top',
    icon: '⬆️',
    label: '回到顶部',
    category: 'navigation',
    action: 'scrollToTop'
  },
  {
    id: 'scroll-bottom',
    icon: '⬇️',
    label: '滚动到底',
    category: 'navigation',
    action: 'scrollToBottom'
  },
  {
    id: 'refresh',
    icon: '🔄',
    label: '刷新页面',
    category: 'navigation',
    action: 'refreshPage'
  },
  {
    id: 'get-links',
    icon: '🔗',
    label: '获取链接',
    category: 'extraction',
    action: 'getAllLinks'
  },
  {
    id: 'get-images',
    icon: '🖼️',
    label: '获取图片',
    category: 'extraction',
    action: 'getAllImages'
  }
])

// 计算属性
const categorizedTools = computed(() => {
  const tools = extensionBridge.getAllTools()
  return tools.filter(tool => isWebControlTool(tool))
})

// 初始化
onMounted(async () => {
  await loadCurrentPageInfo()
  await loadEnabledTools()
  initializeToolParams()
})

// 加载当前页面信息
async function loadCurrentPageInfo() {
  try {
    currentPageInfo.value = await extensionBridge.getCurrentPageInfo()
  } catch (error) {
    console.error('获取页面信息失败:', error)
  }
}

// 加载已启用的工具
async function loadEnabledTools() {
  try {
    const stored = await chrome.storage.local.get(['enabledWebTools'])
    enabledTools.value = stored.enabledWebTools || []
  } catch (error) {
    console.error('加载工具状态失败:', error)
  }
}

// 初始化工具参数
function initializeToolParams() {
  categorizedTools.value.forEach(tool => {
    if (!toolParams[tool.name]) {
      toolParams[tool.name] = {}
      
      // 初始化参数默认值
      const quickParams = getQuickParams(tool)
      quickParams.forEach(param => {
        toolParams[tool.name][param.key] = param.defaultValue || getDefaultValueByType(param.type)
      })
    }
  })
}

// 判断是否为网页控制工具
function isWebControlTool(tool: MCPTool): boolean {
  const webToolKeywords = [
    'click', 'scroll', 'navigate', 'screenshot', 'element', 
    'page', 'browser', 'tab', 'window', 'interact', 'search',
    'link', 'image', 'form', 'input', 'button'
  ]
  
  const toolName = tool.name.toLowerCase()
  return webToolKeywords.some(keyword => toolName.includes(keyword))
}

// 获取工具图标
function getToolIcon(toolName: string): string {
  const name = toolName.toLowerCase()
  
  if (name.includes('click') || name.includes('interact')) return '👆'
  if (name.includes('screenshot') || name.includes('capture')) return '📸'
  if (name.includes('scroll')) return '📜'
  if (name.includes('navigate') || name.includes('url')) return '🧭'
  if (name.includes('search') || name.includes('find')) return '🔍'
  if (name.includes('element')) return '🎯'
  if (name.includes('form') || name.includes('input')) return '📝'
  if (name.includes('link')) return '🔗'
  if (name.includes('image')) return '🖼️'
  
  return '🔧'
}

// 格式化工具名称
function formatToolName(toolName: string): string {
  return toolName
    .replace(/^chrome_/, '')
    .replace(/_/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase())
}

// 获取快速参数
function getQuickParams(tool: MCPTool): any[] {
  const schema = tool.inputSchema
  if (!schema.properties) return []
  
  const quickParams: any[] = []
  const maxQuickParams = 3 // 最多显示3个快速参数
  
  Object.entries(schema.properties).slice(0, maxQuickParams).forEach(([key, prop]: [string, any]) => {
    quickParams.push({
      key,
      label: prop.description || key,
      type: mapSchemaTypeToInputType(prop.type),
      placeholder: prop.description,
      defaultValue: prop.default,
      min: prop.minimum,
      max: prop.maximum,
      options: prop.enum?.map((value: any) => ({ value, label: value }))
    })
  })
  
  return quickParams
}

// 映射schema类型到输入类型
function mapSchemaTypeToInputType(schemaType: string): string {
  const typeMap: Record<string, string> = {
    'string': 'text',
    'number': 'number',
    'integer': 'number',
    'boolean': 'checkbox'
  }
  return typeMap[schemaType] || 'text'
}

// 根据类型获取默认值
function getDefaultValueByType(type: string): any {
  const defaultMap: Record<string, any> = {
    'text': '',
    'number': 0,
    'checkbox': false,
    'select': ''
  }
  return defaultMap[type] || ''
}

// 切换工具启用状态
async function toggleTool(toolName: string, event: Event) {
  const target = event.target as HTMLInputElement
  const isEnabled = target.checked
  
  if (isEnabled) {
    enabledTools.value.push(toolName)
  } else {
    enabledTools.value = enabledTools.value.filter(name => name !== toolName)
  }
  
  // 保存到storage
  await chrome.storage.local.set({ enabledWebTools: enabledTools.value })
}

// 执行快速操作
async function executeQuickAction(action: any) {
  isExecuting.value = true
  
  try {
    const result = await extensionBridge.controlWebPage(action.action, {})
    lastExecutionResult.value = {
      success: result.success,
      data: result.data,
      error: result.error,
      executionTime: result.executionTime || 0,
      toolName: action.label,
      callId: Date.now().toString()
    }
  } catch (error) {
    lastExecutionResult.value = {
      success: false,
      error: error instanceof Error ? error.message : '执行失败',
      executionTime: 0,
      toolName: action.label,
      callId: Date.now().toString()
    }
  } finally {
    isExecuting.value = false
  }
}

// 执行工具（带参数）
async function executeToolWithParams(toolName: string) {
  isExecuting.value = true
  
  try {
    const params = toolParams[toolName] || {}
    const result = await extensionBridge.callTool(toolName, params)
    lastExecutionResult.value = result
  } catch (error) {
    lastExecutionResult.value = {
      success: false,
      error: error instanceof Error ? error.message : '执行失败',
      executionTime: 0,
      toolName,
      callId: Date.now().toString()
    }
  } finally {
    isExecuting.value = false
  }
}

// 刷新页面信息
async function refreshPageInfo() {
  await loadCurrentPageInfo()
}

// 格式化执行时间
function formatExecutionTime(time: number): string {
  if (time < 1000) {
    return `${time}ms`
  }
  return `${(time / 1000).toFixed(2)}s`
}
</script>

<style scoped>
.webpage-controller {
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1f2937;
}

.title-icon {
  font-size: 1.4rem;
}

.subsection-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #374151;
}

/* 页面信息样式 */
.page-info {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.page-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.page-favicon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
}

.page-details {
  flex: 1;
}

.page-title {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: #1f2937;
}

.page-url {
  font-size: 0.8rem;
  color: #6b7280;
  margin: 0;
  word-break: break-all;
}

.refresh-btn {
  padding: 0.5rem;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.refresh-btn:hover {
  background: #f9fafb;
}

/* 快速操作样式 */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.quick-action-btn:hover {
  border-color: #667eea;
  transform: translateY(-1px);
}

.quick-action-btn.capture {
  border-color: #10b981;
}

.quick-action-btn.navigation {
  border-color: #3b82f6;
}

.quick-action-btn.extraction {
  border-color: #8b5cf6;
}

.action-icon {
  font-size: 1.5rem;
}

.action-label {
  font-size: 0.8rem;
  font-weight: 500;
}

/* 工具开关样式 */
.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.tool-toggle-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  background: white;
}

.tool-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.tool-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tool-icon {
  font-size: 1.2rem;
}

.tool-name {
  font-weight: 500;
  color: #1f2937;
}

.tool-description {
  font-size: 0.85rem;
  color: #6b7280;
  margin-bottom: 0.75rem;
}

/* 开关样式 */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.2s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.2s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #667eea;
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* 参数控制样式 */
.tool-params {
  border-top: 1px solid #e5e7eb;
  padding-top: 0.75rem;
  margin-top: 0.75rem;
}

.param-control {
  margin-bottom: 0.75rem;
}

.param-label {
  display: block;
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
  color: #374151;
}

.param-input, .param-select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.85rem;
}

.execute-tool-btn {
  width: 100%;
  padding: 0.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.execute-tool-btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.execute-tool-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 执行结果样式 */
.execution-result {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
}

.result-content {
  border-radius: 8px;
  padding: 1rem;
  margin-top: 0.5rem;
}

.result-content.success {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
}

.result-content.error {
  background: #fef2f2;
  border: 1px solid #fecaca;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.result-icon {
  font-size: 1.2rem;
}

.result-tool {
  font-weight: 500;
}

.result-time {
  font-size: 0.8rem;
  color: #6b7280;
  margin-left: auto;
}

.result-data pre {
  background: rgba(0, 0, 0, 0.05);
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  overflow-x: auto;
}

.error-message {
  color: #dc2626;
  font-weight: 500;
  margin: 0;
}
</style>