/**
 * 连接管理器 - 统一管理与chrome-mcp-server的连接
 * 提供自动检测、连接和重连功能
 */

import ChromeExtensionBridge from './chromeExtensionBridge'
import { MCPClient } from './mcpClient'
import type { MCPTool, MCPServerInfo } from '@/types/mcp'

export type ConnectionType = 'extension' | 'direct' | 'none'

export interface ConnectionStatus {
  type: ConnectionType
  connected: boolean
  toolsCount: number
  serverInfo: MCPServerInfo | null
  lastChecked: number
}

export class ConnectionManager {
  private static instance: ConnectionManager
  private extensionBridge: ChromeExtensionBridge
  private mcpClient: MCPClient | null = null
  private currentConnection: ConnectionType = 'none'
  private connectionStatus: ConnectionStatus = {
    type: 'none',
    connected: false,
    toolsCount: 0,
    serverInfo: null,
    lastChecked: 0
  }

  private constructor() {
    this.extensionBridge = ChromeExtensionBridge.getInstance()
  }

  static getInstance(): ConnectionManager {
    if (!ConnectionManager.instance) {
      ConnectionManager.instance = new ConnectionManager()
    }
    return ConnectionManager.instance
  }

  /**
   * 自动检测并建立最佳连接
   */
  async autoConnect(): Promise<ConnectionStatus> {
    console.log('🔍 开始自动连接检测...')

    // 方法1: 尝试连接chrome-mcp-server插件
    try {
      console.log('🔌 尝试连接chrome-mcp-server插件...')
      const serverInfo = await this.extensionBridge.detectAndConnect()

      this.currentConnection = 'extension'
      this.connectionStatus = {
        type: 'extension',
        connected: true,
        toolsCount: this.extensionBridge.getToolsCount(),
        serverInfo,
        lastChecked: Date.now()
      }

      console.log('✅ chrome-mcp-server插件连接成功')
      await this.saveConnectionStatus()
      return this.connectionStatus

    } catch (extensionError: unknown) {
      const msg = extensionError instanceof Error ? extensionError.message : String(extensionError)
      console.log('⚠️ chrome-mcp-server插件连接失败:', msg)
    }

    // 方法2: 尝试直连MCP服务器
    try {
      console.log('🔌 尝试直连MCP服务器...')
      const config = await this.getStoredConfig()

      this.mcpClient = MCPClient.getInstance(config.serverEndpoint)
      const serverInfo = await this.mcpClient.initialize()

      if (serverInfo.available) {
        this.currentConnection = 'direct'
        this.connectionStatus = {
          type: 'direct',
          connected: true,
          toolsCount: this.mcpClient.getToolsCount(),
          serverInfo,
          lastChecked: Date.now()
        }

        console.log('✅ MCP服务器直连成功')
        await this.saveConnectionStatus()
        return this.connectionStatus
      }

    } catch (directError: unknown) {
      const msg = directError instanceof Error ? directError.message : String(directError)
      console.log('⚠️ MCP服务器直连失败:', msg)
    }

    // 所有连接方式都失败
    this.currentConnection = 'none'
    this.connectionStatus = {
      type: 'none',
      connected: false,
      toolsCount: 0,
      serverInfo: null,
      lastChecked: Date.now()
    }

    console.log('❌ 所有连接方式都失败')
    await this.saveConnectionStatus()
    return this.connectionStatus
  }

  /**
   * 获取当前连接状态
   */
  getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus }
  }

  /**
   * 获取所有可用工具
   */
  getAllTools(): MCPTool[] {
    switch (this.currentConnection) {
      case 'extension':
        return this.extensionBridge.getAllTools()
      case 'direct':
        return this.mcpClient?.getAllTools() || []
      default:
        return []
    }
  }

  /**
   * 按分类获取工具
   */
  getToolsByCategory(): Record<string, MCPTool[]> {
    switch (this.currentConnection) {
      case 'extension':
        return this.extensionBridge.getToolsByCategory()
      case 'direct':
        return this.mcpClient?.getToolsByCategory() || {}
      default:
        return {}
    }
  }

  /**
   * 调用工具
   */
  async callTool(toolName: string, parameters: Record<string, any>): Promise<any> {
    if (!this.connectionStatus.connected) {
      throw new Error('未建立连接，无法调用工具')
    }

    switch (this.currentConnection) {
      case 'extension':
        return await this.extensionBridge.callTool(toolName, parameters)
      case 'direct':
        if (!this.mcpClient) {
          throw new Error('MCP客户端未初始化')
        }
        return await this.mcpClient.callTool(toolName, parameters)
      default:
        throw new Error('无可用连接')
    }
  }

  /**
   * 控制网页（仅插件模式支持）
   */
  async controlWebPage(action: string, params: any): Promise<any> {
    if (this.currentConnection !== 'extension') {
      throw new Error('网页控制功能仅在chrome-mcp-server插件模式下可用')
    }

    return await this.extensionBridge.controlWebPage(action, params)
  }

  /**
   * 获取当前页面信息（仅插件模式支持）
   */
  async getCurrentPageInfo(): Promise<any> {
    if (this.currentConnection !== 'extension') {
      throw new Error('页面信息获取功能仅在chrome-mcp-server插件模式下可用')
    }

    return await this.extensionBridge.getCurrentPageInfo()
  }

  /**
   * 刷新工具列表
   */
  async refreshTools(): Promise<MCPTool[]> {
    switch (this.currentConnection) {
      case 'extension':
        const tools = await this.extensionBridge.refreshTools()
        this.connectionStatus.toolsCount = tools.length
        await this.saveConnectionStatus()
        return tools
      case 'direct':
        if (!this.mcpClient) {
          throw new Error('MCP客户端未初始化')
        }
        const directTools = await this.mcpClient.refreshTools()
        this.connectionStatus.toolsCount = directTools.length
        await this.saveConnectionStatus()
        return directTools
      default:
        return []
    }
  }

  /**
   * 重新连接
   */
  async reconnect(): Promise<ConnectionStatus> {
    console.log('🔄 重新连接...')
    return await this.autoConnect()
  }

  /**
   * 检查连接健康状态
   */
  async checkHealth(): Promise<boolean> {
    try {
      switch (this.currentConnection) {
        case 'extension':
          return this.extensionBridge.isConnected()
        case 'direct':
          return this.mcpClient?.isConnected() || false
        default:
          return false
      }
    } catch (error) {
      console.error('连接健康检查失败:', error)
      return false
    }
  }

  /**
   * 获取存储的配置
   */
  private async getStoredConfig(): Promise<any> {
    const result = await chrome.storage.local.get(['config'])
    return result.config || {
      serverEndpoint: 'http://127.0.0.1:12306',
      autoRefresh: true,
      refreshInterval: 30000
    }
  }

  /**
   * 保存连接状态
   */
  private async saveConnectionStatus(): Promise<void> {
    await chrome.storage.local.set({
      connectionStatus: this.connectionStatus
    })
  }

  /**
   * 获取连接类型的显示名称
   */
  getConnectionTypeName(): string {
    switch (this.currentConnection) {
      case 'extension':
        return 'Chrome MCP Server 插件'
      case 'direct':
        return 'MCP 服务器直连'
      default:
        return '未连接'
    }
  }

  /**
   * 是否支持网页控制功能
   */
  supportsWebControl(): boolean {
    return this.currentConnection === 'extension'
  }
}

export default ConnectionManager
