<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg128" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea"/>
      <stop offset="100%" style="stop-color:#764ba2"/>
    </linearGradient>
  </defs>
  <rect width="128" height="128" rx="26" fill="url(#bg128)"/>
  <circle cx="48" cy="45" r="8" fill="white"/>
  <circle cx="80" cy="45" r="8" fill="white"/>
  <circle cx="64" cy="75" r="8" fill="white"/>
  <line x1="48" y1="45" x2="80" y2="45" stroke="white" stroke-width="4" stroke-linecap="round"/>
  <line x1="64" y1="55" x2="64" y2="67" stroke="white" stroke-width="4" stroke-linecap="round"/>
</svg>