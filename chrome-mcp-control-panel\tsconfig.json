{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "types/**/*"], "exclude": ["node_modules", "dist"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["chrome", "vite/client"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "lib": ["ES2022", "DOM", "DOM.Iterable"], "target": "ES2022", "useDefineForClassFields": true, "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true}}