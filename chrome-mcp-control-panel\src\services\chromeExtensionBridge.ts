/**
 * Chrome Extension Bridge Service
 * 专门用于与chrome-mcp-server插件通讯的桥接服务
 */

import type { MCPTool, MCPToolResult, MCPServerInfo } from '@/types/mcp'

export class ChromeExtensionBridge {
  private static instance: ChromeExtensionBridge
  private chromeExtensionId: string | null = null
  private isConnectedFlag = false
  private tools: MCPTool[] = []
  private serverInfo: MCPServerInfo | null = null

  // 固定主扩展ID（由 key 派生，保持不变）
  private readonly KNOWN_EXTENSION_IDS = [
    'hbdgbgagpkpjffpklnamcljpakneikee'
  ]

  private constructor() {}

  static getInstance(): ChromeExtensionBridge {
    if (!ChromeExtensionBridge.instance) {
      ChromeExtensionBridge.instance = new ChromeExtensionBridge()
    }
    return ChromeExtensionBridge.instance
  }

  /**
   * 检测并连接chrome-mcp-server插件
   */
  async detectAndConnect(): Promise<MCPServerInfo> {
    console.log('🔍 正在检测chrome-mcp-server插件...')

    // 方法1: 尝试已知的扩展ID
    for (const extensionId of this.KNOWN_EXTENSION_IDS) {
      if (await this.tryConnectToExtension(extensionId)) {
        return this.serverInfo!
      }
    }

    // 方法2: 尝试通过content script通讯
    const detectedId = await this.detectThroughContentScript()
    if (detectedId && await this.tryConnectToExtension(detectedId)) {
      return this.serverInfo!
    }

    // 方法3: 尝试通过storage检测
    const storageId = await this.detectThroughStorage()
    if (storageId && await this.tryConnectToExtension(storageId)) {
      return this.serverInfo!
    }

    throw new Error('未找到chrome-mcp-server插件，请确保插件已安装并启用')
  }

  /**
   * 尝试连接到指定的扩展
   */
  private async tryConnectToExtension(extensionId: string): Promise<boolean> {
    try {
      console.log(`🔗 尝试连接扩展: ${extensionId}`)

      // 发送ping消息测试连接
      const pingResponse = await this.sendMessageToExtension(extensionId, {
        action: 'ping',
        source: 'chrome-mcp-control-panel'
      })

      if (pingResponse && pingResponse.success) {
        console.log(`✅ 成功连接到扩展: ${extensionId}`)
        this.chromeExtensionId = extensionId

        // 获取扩展状态和工具列表
        await this.initializeConnection()
        return true
      }
    } catch (error) {
      console.log(`❌ 连接扩展失败: ${extensionId}`, error)
    }
    return false
  }

  /**
   * 通过content script检测扩展
   */
  private async detectThroughContentScript(): Promise<string | null> {
    try {
      // 向当前活动标签页注入检测脚本
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (!tab.id) return null

      const result = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: () => {
          // 检查页面中是否有chrome-mcp相关的标识
          const mcpElements = document.querySelectorAll('[data-chrome-mcp-extension-id]')
          if (mcpElements.length > 0) {
            return mcpElements[0].getAttribute('data-chrome-mcp-extension-id')
          }

          // 检查window对象中的标识
          if ((window as any).chromeMcpExtensionId) {
            return (window as any).chromeMcpExtensionId
          }

          return null
        }
      })

      return result[0]?.result || null
    } catch (error) {
      console.log('通过content script检测失败:', error)
      return null
    }
  }

  /**
   * 通过storage检测扩展ID
   */
  private async detectThroughStorage(): Promise<string | null> {
    try {
      const result = await chrome.storage.local.get(['chromeMcpExtensionId'])
      return result.chromeMcpExtensionId || null
    } catch (error) {
      console.log('通过storage检测失败:', error)
      return null
    }
  }

  /**
   * 初始化与扩展的连接
   */
  private async initializeConnection(): Promise<void> {
    if (!this.chromeExtensionId) {
      throw new Error('扩展ID未设置')
    }

    try {
      // 获取扩展状态
      const statusResponse = await this.sendMessageToExtension(this.chromeExtensionId, {
        action: 'getStatus'
      })

      if (!statusResponse.success) {
        throw new Error('获取扩展状态失败')
      }

      // 获取工具列表
      const toolsResponse = await this.sendMessageToExtension(this.chromeExtensionId, {
        action: 'getAvailableTools'
      })

      if (!toolsResponse.success) {
        throw new Error('获取工具列表失败')
      }

      this.tools = toolsResponse.data.tools || []
      this.isConnectedFlag = true

      this.serverInfo = {
        name: 'Chrome MCP Server Extension',
        version: statusResponse.data.version || '1.0.0',
        available: true,
        endpoint: `chrome-extension://${this.chromeExtensionId}`,
        lastChecked: Date.now()
      }

      // 保存扩展ID到storage以便下次快速连接
      await chrome.storage.local.set({
        chromeMcpExtensionId: this.chromeExtensionId
      })

      // A) 自动拉起原生主机，启动本地MCP服务（避免手动步骤）
      try {
        await this.sendMessageToExtension(this.chromeExtensionId, { action: 'connectNative' })
        console.log('🟢 已请求主扩展连接原生主机')
      } catch (e) {
        console.log('⚠️ 自动连接原生主机失败（可忽略或稍后重试）', e)
      }

      console.log(`✅ 初始化完成，发现 ${this.tools.length} 个工具`)
    } catch (error) {
      this.isConnectedFlag = false
      throw error
    }
  }

  /**
   * 向扩展发送消息
   */
  private async sendMessageToExtension(extensionId: string, message: any): Promise<any> {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(extensionId, message, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message))
        } else {
          resolve(response)
        }
      })
    })
  }

  /**
   * 调用工具
   */
  async callTool(toolName: string, parameters: Record<string, any>): Promise<MCPToolResult> {
    if (!this.isConnectedFlag || !this.chromeExtensionId) {
      throw new Error('未连接到chrome-mcp-server插件')
    }

    try {
      console.log(`🔧 调用工具: ${toolName}`, parameters)

      const response = await this.sendMessageToExtension(this.chromeExtensionId, {
        action: 'callTool',
        toolName,
        parameters,
        timestamp: Date.now()
      })

      if (!response.success) {
        throw new Error(response.error || '工具调用失败')
      }

      console.log(`✅ 工具调用成功: ${toolName}`)
      return {
        success: true,
        data: response.data,
        executionTime: response.executionTime || 0,
        toolName,
        callId: response.callId || Date.now().toString()
      }
    } catch (error) {
      console.error(`❌ 工具调用失败: ${toolName}`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        executionTime: 0,
        toolName,
        callId: Date.now().toString()
      }
    }
  }

  /**
   * 获取当前页面信息（用于网页控制）
   */
  async getCurrentPageInfo(): Promise<any> {
    if (!this.isConnectedFlag || !this.chromeExtensionId) {
      throw new Error('未连接到chrome-mcp-server插件')
    }

    const response = await this.sendMessageToExtension(this.chromeExtensionId, {
      action: 'getCurrentPageInfo'
    })

    return response.data
  }

  /**
   * 控制当前网页
   */
  async controlWebPage(action: string, params: any): Promise<any> {
    if (!this.isConnectedFlag || !this.chromeExtensionId) {
      throw new Error('未连接到chrome-mcp-server插件')
    }

    const response = await this.sendMessageToExtension(this.chromeExtensionId, {
      action: 'controlWebPage',
      webAction: action,
      params
    })

    return response
  }

  /**
   * 刷新工具列表
   */
  async refreshTools(): Promise<MCPTool[]> {
    if (!this.isConnectedFlag || !this.chromeExtensionId) {
      throw new Error('未连接到chrome-mcp-server插件')
    }

    const response = await this.sendMessageToExtension(this.chromeExtensionId, {
      action: 'refreshTools'
    })

    if (response.success) {
      this.tools = response.data.tools || []
    }

    return this.tools
  }

  // Getters
  isConnected(): boolean {
    return this.isConnectedFlag
  }

  getAllTools(): MCPTool[] {
    return this.tools
  }

  getToolsCount(): number {
    return this.tools.length
  }

  getServerInfo(): MCPServerInfo | null {
    return this.serverInfo
  }

  getExtensionId(): string | null {
    return this.chromeExtensionId
  }

  /**
   * 按分类获取工具
   */
  getToolsByCategory(): Record<string, MCPTool[]> {
    const categories: Record<string, MCPTool[]> = {}

    this.tools.forEach(tool => {
      const category = this.categorizeToolByName(tool.name)
      if (!categories[category]) {
        categories[category] = []
      }
      categories[category].push(tool)
    })

    return categories
  }

  /**
   * 根据工具名称分类
   */
  private categorizeToolByName(toolName: string): string {
    const name = toolName.toLowerCase()

    if (name.includes('click') || name.includes('interact') || name.includes('element')) {
      return '页面交互'
    }
    if (name.includes('screenshot') || name.includes('capture')) {
      return '页面截图'
    }
    if (name.includes('navigate') || name.includes('url') || name.includes('page')) {
      return '页面导航'
    }
    if (name.includes('search') || name.includes('find')) {
      return '搜索功能'
    }
    if (name.includes('bookmark') || name.includes('favorite')) {
      return '书签管理'
    }
    if (name.includes('history')) {
      return '历史记录'
    }
    if (name.includes('network') || name.includes('request')) {
      return '网络请求'
    }
    if (name.includes('debug') || name.includes('console')) {
      return '调试工具'
    }

    return '其他工具'
  }
}

export default ChromeExtensionBridge
