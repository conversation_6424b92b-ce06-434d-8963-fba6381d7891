# Chrome MCP Control Panel

**独立的Chrome MCP工具可视化控制面板**

> 这是一个完全独立的Chrome扩展项目，专门为chrome-mcp项目的28+工具提供直观的可视化控制界面。

## 🎯 **ultrathink设计理念**

### **解决的核心问题**
1. **AI指令理解偏差** - 自然语言控制28个工具容易产生歧义
2. **功能发现困难** - 用户不知道有哪些具体功能可用  
3. **参数设置复杂** - 每个工具的参数组合多样，难以精确控制
4. **状态不可见** - 无法实时查看工具执行状态和结果

### **自适应架构特性**
- ✅ **自动工具发现** - 动态获取MCP服务器的工具列表，零硬编码
- ✅ **动态UI生成** - 基于工具schema自动生成用户界面
- ✅ **实时同步更新** - 监听工具变化，自动更新界面
- ✅ **完全独立部署** - 与主项目零耦合，独立开发和维护

## 📁 项目结构

```
chrome-mcp-control-panel/
├── src/
│   ├── background.ts           # Chrome扩展后台服务
│   ├── components/             # Vue组件
│   │   ├── PopupApp.vue       # 弹窗应用
│   │   └── OptionsApp.vue     # 选项页面应用
│   ├── services/              # 核心服务
│   │   ├── mcpClient.ts       # MCP客户端 (自适应工具发现)
│   │   └── uiGenerator.ts     # 动态UI生成服务
│   ├── types/                 # TypeScript类型定义
│   │   └── mcp.ts            # MCP相关类型
│   ├── pages/                 # 页面入口
│   │   ├── popup.html/ts     # 弹窗页面
│   │   └── options.html/ts   # 选项页面
│   └── assets/               # 静态资源
├── public/
│   ├── manifest.json         # Chrome扩展清单
│   └── icons/               # 扩展图标
├── scripts/                 # 构建和部署脚本
│   ├── build.js            # 独立构建脚本
│   └── install.js          # 安装脚本
├── docs/                   # 项目文档
└── dist/                   # 构建输出目录
```

## 🚀 快速开始

### 前置条件
- Node.js >= 18.0.0
- Chrome浏览器
- chrome-mcp主扩展正在运行

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建扩展
```bash
npm run build
```

### 安装到Chrome
```bash
npm run install-extension
```

### 打包发布
```bash
npm run package
```

## 🏗️ 核心架构

### 1. 自适应工具发现机制

```typescript
// MCP客户端自动发现所有可用工具
const mcpClient = MCPClient.getInstance()
const tools = await mcpClient.refreshTools()

// 监听工具更新
mcpClient.onToolsUpdated((newTools) => {
  updateUI(newTools)
})
```

### 2. 动态UI生成系统

```typescript
// 基于工具schema生成界面
const uiGenerator = UIGeneratorService.getInstance()
const formConfig = uiGenerator.generateFormConfig(tool)

// 自动生成表单字段、验证规则、快捷操作
const fields = formConfig.fields
const validation = formConfig.validation
const quickActions = uiGenerator.generateQuickActions(tool)
```

### 3. 实时通信架构

```
用户界面 ←→ Chrome扩展 ←→ MCP客户端 ←→ chrome-mcp服务器 ←→ Chrome浏览器
```

## 📖 使用指南

### 弹窗界面 (Quick Access)
- **快速状态查看** - 连接状态、工具数量、服务器信息
- **快速操作按钮** - 常用功能的一键执行
- **最近使用工具** - 快速访问历史操作
- **快捷跳转** - 一键打开完整控制面板

### 选项页面 (Full Control Panel)
- **工具分类展示** - 按功能分组显示所有工具
- **动态表单界面** - 基于schema自动生成的参数配置
- **实时执行反馈** - 工具执行状态和结果显示
- **收藏和历史** - 个性化工具管理
- **配置管理** - 服务器设置和界面配置

## 🔧 开发指南

### 添加新功能
1. **工具自动识别** - 新工具会自动出现在界面中
2. **界面自动生成** - 基于schema自动创建配置界面
3. **无需代码修改** - 完全数据驱动的架构

### 自定义界面
```typescript
// 扩展UI生成器以支持特殊字段类型
uiGenerator.addFieldType('coordinates', CoordinatesField)
uiGenerator.addFieldPreset('selector', customSelectors)
```

### 调试和测试
```bash
# 开发模式 - 热重载
npm run dev

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 构建测试
npm run build
```

## 🎨 技术栈

- **前端框架**: Vue 3 + TypeScript + Composition API
- **构建工具**: Vite + vite-plugin-web-extension  
- **UI库**: Headless UI + Heroicons
- **通信协议**: MCP (Model Context Protocol)
- **Chrome APIs**: Extensions API + Runtime API + Storage API

## 🔄 版本管理

### 自动版本同步
- 扩展版本自动从`package.json`读取
- 构建时自动更新`manifest.json`
- 支持语义化版本管理

### 更新策略
1. **开发更新** - 代码修改后`npm run build`，在Chrome中刷新扩展
2. **发布更新** - `npm run package`生成新版本安装包
3. **自动更新** - 支持Chrome Web Store自动更新机制

## 📊 性能特性

- **按需加载** - 组件和工具界面按需生成
- **缓存机制** - 工具定义和配置本地缓存
- **实时同步** - WebSocket/SSE实时监听工具变化
- **内存优化** - 大列表虚拟滚动，避免性能问题

## 🛡️ 安全特性

- **权限最小化** - 只请求必要的Chrome权限
- **本地通信** - 仅与本地MCP服务器通信
- **输入验证** - 所有参数自动验证和清洗
- **错误隔离** - 工具执行错误不影响扩展稳定性

## 🚧 故障排除

### 常见问题

#### 1. 连接失败
- 确保chrome-mcp主扩展正在运行
- 检查MCP服务器状态 (http://127.0.0.1:12306)
- 尝试重新连接按钮

#### 2. 工具不显示
- 点击刷新工具列表按钮
- 检查MCP服务器工具注册状态
- 查看浏览器控制台错误信息

#### 3. 界面异常
- 清除扩展存储数据
- 重新加载扩展
- 检查manifest.json配置

### 调试工具
```bash
# 查看扩展日志
chrome://extensions/ -> 控制面板扩展 -> 背景页 -> 检查视图

# 查看MCP通信
开发者工具 -> Network -> 筛选127.0.0.1:12306

# 查看存储数据  
开发者工具 -> Application -> Storage -> Extension Storage
```

## 🤝 贡献指南

### 开发流程
1. Fork本项目
2. 创建feature分支
3. 提交代码并测试
4. 发起Pull Request

### 代码规范
- 使用TypeScript严格模式
- 遵循ESLint配置
- 组件采用Vue 3 Composition API
- 服务采用单例模式

## 📄 许可证

MIT License - 完全开源，可自由使用和修改。

## 🎉 总结

这个独立的控制面板项目完美解决了chrome-mcp工具的可视化控制需求：

- ✅ **零维护成本** - 新工具自动识别，界面自动生成
- ✅ **完全解耦** - 与主项目独立开发，不污染源码
- ✅ **用户友好** - 直观的可视化界面，专业级控制体验
- ✅ **技术先进** - 基于现代Web技术栈，性能优异
- ✅ **扩展性强** - 数据驱动架构，易于定制和扩展

**从"记忆28个工具命令"到"可视化点击控制"，这就是我们要交付的变革性用户体验！** 🚀