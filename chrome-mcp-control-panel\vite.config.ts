import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import webExtension from 'vite-plugin-web-extension'

export default defineConfig({
  plugins: [
    vue(),
    webExtension({
      manifest: './public/manifest.json',
      watchFilePaths: ['src/**/*'],
      additionalInputs: [
        'src/pages/options.html',
        'src/pages/popup.html',
        'src/pages/sidebar.html'
      ]
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/pages': resolve(__dirname, 'src/pages'),
      '@/utils': resolve(__dirname, 'src/utils'),
      '@/services': resolve(__dirname, 'src/services'),
      '@/types': resolve(__dirname, 'src/types')
    }
  },
  build: {
    outDir: 'dist'
  },
  server: {
    port: 3000,
    host: 'localhost'
  },
  define: {
    __VUE_OPTIONS_API__: false,
    __VUE_PROD_DEVTOOLS__: false
  }
})
