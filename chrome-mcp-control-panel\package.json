{"name": "chrome-mcp-control-panel", "version": "1.0.0", "description": "独立的Chrome MCP工具可视化控制面板", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "cross-env ROLLUP_NATIVE=false tsc && cross-env ROLLUP_NATIVE=false vite build", "postbuild": "node scripts/postbuild-fix.js", "preview": "vite preview", "lint": "eslint . --ext ts,tsx,vue --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx,vue --fix", "type-check": "tsc --noEmit", "package": "node scripts/build.js", "install-extension": "node scripts/install.js"}, "dependencies": {"@headlessui/vue": "^1.7.0", "@heroicons/vue": "^2.0.0", "axios": "^1.6.0", "pinia": "^2.1.0", "vue": "^3.4.0", "vue-router": "^4.2.0"}, "devDependencies": {"@rollup/rollup-win32-x64-msvc": "^4.46.2", "@types/chrome": "^0.0.250", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/tsconfig": "^0.4.0", "cross-env": "^10.0.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.0", "rollup": "^4.46.2", "typescript": "~5.2.0", "vite": "^5.0.0", "vite-plugin-web-extension": "^4.0.0"}, "keywords": ["chrome-extension", "mcp", "browser-automation", "control-panel", "vue3"], "author": "Chrome MCP Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/chrome-mcp-control-panel.git"}}