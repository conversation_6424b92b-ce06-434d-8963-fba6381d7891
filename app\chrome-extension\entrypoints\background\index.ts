import { initNativeHostListener, connectNativeHost } from './native-host';
import {
  initSemanticSimilarityListener,
  initializeSemanticEngineIfCached,
} from './semantic-similarity';
import { initStorageManagerListener } from './storage-manager';
import { cleanupModelCache } from '@/utils/semantic-similarity-engine';
import { handleCallTool } from './tools';
import { TOOL_SCHEMAS } from 'chrome-mcp-shared';

/**
 * Background script entry point
 * Initializes all background services and listeners
 */
export default defineBackground(() => {
  // Initialize core services
  initNativeHostListener();
  initSemanticSimilarityListener();
  initStorageManagerListener();

  // Conditionally initialize semantic similarity engine if model cache exists
  initializeSemanticEngineIfCached()
    .then((initialized) => {
      if (initialized) {
        console.log('Background: Semantic similarity engine initialized from cache');
      } else {
        console.log(
          'Background: Semantic similarity engine initialization skipped (no cache found)',
        );
      }
    })
    .catch((error) => {
      console.warn('Background: Failed to conditionally initialize semantic engine:', error);
    });

  // Externally connectable message bridge for control panel extension
  chrome.runtime.onMessageExternal.addListener((request, sender, sendResponse) => {
    const action = request?.action as string | undefined;
    const manifest = chrome.runtime.getManifest();

    const reply = (resp: any) => {
      try {
        sendResponse(resp);
      } catch (_) {
        // ignore
      }
    };

    if (!action) {
      reply({ success: false, error: 'Invalid request: missing action' });
      return true;
    }

    (async () => {
      try {
        switch (action) {
          case 'ping': {
            reply({ success: true, message: 'pong' });
            break;
          }
          case 'getStatus': {
            reply({
              success: true,
              data: {
                name: manifest.name,
                version: manifest.version,
                available: true,
              },
            });
            break;
          }
          case 'connectNative': {
            // 允许外部扩展要求连接原生主机（用于拉起本地 MCP 服务）
            connectNativeHost();
            reply({ success: true });
            break;
          }
          case 'getAvailableTools': {
            // 返回共享定义中的工具清单（名称/描述/输入模式）
            reply({ success: true, data: { tools: TOOL_SCHEMAS } });
            break;
          }
          case 'callTool': {
            const name = request?.toolName as string;
            const args = request?.parameters ?? {};
            if (!name) {
              reply({ success: false, error: 'Missing toolName' });
              break;
            }
            try {
              const result = await handleCallTool({ name, args });
              reply({ success: true, data: result });
            } catch (err: any) {
              reply({ success: false, error: err?.message || String(err) });
            }
            break;
          }
          case 'refreshTools': {
            reply({ success: true, data: { tools: TOOL_SCHEMAS } });
            break;
          }
          // 可按需扩展：getCurrentPageInfo / controlWebPage
          default:
            reply({ success: false, error: `Unknown action: ${action}` });
        }
      } catch (e: any) {
        reply({ success: false, error: e?.message || String(e) });
      }
    })();

    return true; // keep channel open for async
  });

  // Initial cleanup on startup
  cleanupModelCache().catch((error) => {
    console.warn('Background: Initial cache cleanup failed:', error);
  });
});
